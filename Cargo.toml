[package]
name = "kvm-dma-radar"
version = "0.1.0"
edition = "2021"

[dependencies]
memflow = "0.2"
memflow-qemu = "0.2"
tokio = { version = "1.0", features = ["full"] }
tokio-tungstenite = "0.20"
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
anyhow = "1.0"
log = "0.4"
env_logger = "0.10"
clap = { version = "4.0", features = ["derive"] }
warp = "0.3"
futures-util = "0.3"
uuid = { version = "1.0", features = ["v4"] }

[dependencies.memflow-win32]
version = "0.2"
features = ["std", "serde_support"]

[[bin]]
name = "radar-server"
path = "src/main.rs"

[profile.release]
opt-level = 3
lto = true
codegen-units = 1
