# KVM-DMA 雷达系统

基于虚拟化技术的CS2游戏雷达系统，使用MemFlow-QEMU实现虚拟机内存读取，完全绕过传统反作弊检测。

## 系统架构

```
Ubuntu Host (物理机)
├── KVM虚拟机 (Win10)
│   └── CS2游戏进程
├── MemFlow-QEMU (内存读取)
├── Rust雷达服务器
└── Web雷达界面
```

## 核心特性

- **虚拟化隔离**: 游戏运行在KVM虚拟机中，雷达运行在宿主机
- **内存直读**: 使用MemFlow直接读取虚拟机内存，无需注入
- **实时雷达**: WebSocket实时推送玩家位置数据
- **局域网共享**: 支持多设备同时访问雷达界面
- **零检测风险**: 完全在虚拟机外部运行，无法被反作弊检测

## 环境要求

### 硬件要求
- CPU: 支持虚拟化的x86_64处理器
- 内存: 至少16GB (宿主机8GB + 虚拟机8GB)
- 显卡: 支持GPU直通的独立显卡 (推荐NVIDIA)

### 软件要求
- Ubuntu 20.04+ (宿主机)
- KVM/QEMU虚拟化环境
- Windows 10/11 (虚拟机)
- Rust 1.70+
- libvirt开发库

## 安装步骤

### 1. 系统准备

```bash
# 安装KVM虚拟化环境
sudo apt update
sudo apt install qemu-kvm libvirt-daemon-system libvirt-clients bridge-utils

# 安装开发依赖
sudo apt install build-essential pkg-config libvirt-dev

# 安装Rust
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh
source ~/.cargo/env
```

### 2. 虚拟机配置

```bash
# 创建Win10虚拟机 (需要Windows ISO)
virt-install \
  --name win10 \
  --memory 8192 \
  --vcpus 4 \
  --disk size=100 \
  --cdrom /path/to/windows.iso \
  --os-variant win10 \
  --network bridge=virbr0

# 配置GPU直通 (可选，提升游戏性能)
# 编辑虚拟机配置，添加GPU设备
```

### 3. 构建雷达系统

```bash
# 克隆项目
git clone <repository-url>
cd kvm-dma-radar

# 自动构建和配置
chmod +x build.sh
./build.sh all
```

### 4. 启动系统

```bash
# 启动虚拟机
virsh start win10

# 在虚拟机中安装并启动CS2

# 启动雷达系统
cd dist
./start.sh
```

### 5. 访问雷达

打开浏览器访问: `http://localhost:8080`

## 配置说明

### 游戏偏移量更新

CS2游戏更新后需要更新内存偏移量，编辑 `src/game.rs` 中的 `GameOffsets`:

```rust
pub struct GameOffsets {
    // 根据最新游戏版本更新这些偏移量
    pub entity_list: u64,      // 实体列表偏移
    pub local_player: u64,     // 本地玩家偏移
    pub view_matrix: u64,      // 视图矩阵偏移
    // ... 其他偏移量
}
```

### 网络配置

修改 `dist/config.toml` 配置文件:

```toml
[server]
bind_address = "0.0.0.0"  # 监听所有网络接口
port = 8080               # Web服务端口

[vm]
name = "win10"            # 虚拟机名称
```

## 使用说明

### Web雷达界面

- **绿色点**: 本地玩家位置
- **红色点**: 敌方玩家位置  
- **蓝色点**: 队友位置
- **信息面板**: 显示连接状态、FPS、玩家数量等

### 控制按钮

- **切换大小**: 调整雷达显示大小
- **显示信息**: 切换玩家信息显示
- **重新连接**: 重新建立WebSocket连接

### 局域网访问

其他设备可通过以下地址访问雷达:
```
http://<Ubuntu主机IP>:8080
```

## 故障排除

### 常见问题

1. **无法连接虚拟机**
   ```bash
   # 检查虚拟机状态
   virsh list --all
   
   # 检查libvirt权限
   groups | grep libvirt
   ```

2. **找不到游戏进程**
   ```bash
   # 确认CS2进程名称
   # 可能是 cs2.exe 或 csgo.exe
   ```

3. **内存读取失败**
   ```bash
   # 检查MemFlow安装
   memflow-cli list
   
   # 检查QEMU监控套接字
   ls -la /var/lib/libvirt/qemu/
   ```

### 调试模式

启用调试输出:
```bash
./radar-server --debug
```

查看详细日志:
```bash
tail -f radar.log
```

## 安全说明

⚠️ **重要提醒**: 
- 本系统仅用于安全研究和反作弊技术测试
- 请勿在正式游戏环境中使用
- 使用者需承担相应法律责任

## 技术原理

### 虚拟化内存读取

1. **KVM虚拟机**: 游戏运行在隔离的虚拟环境中
2. **MemFlow框架**: 提供跨平台内存读取接口
3. **QEMU连接器**: 通过QEMU监控接口访问虚拟机内存
4. **Win32模块**: 解析Windows进程和内存结构

### 数据流程

```
CS2游戏进程 → 虚拟机内存 → MemFlow读取 → 数据解析 → WebSocket推送 → Web雷达显示
```

### 反检测机制

- **物理隔离**: 雷达代码完全运行在宿主机
- **内存直读**: 不需要注入DLL或修改游戏文件
- **虚拟化隐藏**: 使用反虚拟化技术隐藏虚拟机特征

## 开发说明

### 项目结构

```
src/
├── main.rs          # 主程序入口
├── memory.rs        # 内存读取模块
├── game.rs          # 游戏数据结构
├── radar.rs         # 雷达核心逻辑
└── web.rs           # Web服务器

web/
└── index.html       # 雷达界面

build.sh             # 构建脚本
Cargo.toml           # Rust依赖配置
```

### 添加新游戏支持

1. 在 `game.rs` 中添加新的偏移量结构
2. 实现对应的数据读取逻辑
3. 更新Web界面显示逻辑

### 性能优化

- 调整更新频率: 修改 `update_interval_ms`
- 优化内存读取: 批量读取相关数据
- 减少WebSocket数据量: 只推送变化的数据

## 许可证

本项目仅供学习和研究使用，请遵守相关法律法规。

## 贡献

欢迎提交Issue和Pull Request来改进项目。

---

**免责声明**: 本项目仅用于技术研究和教育目的，开发者不对任何滥用行为承担责任。
