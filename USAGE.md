# KVM-DMA雷达系统使用指南

## 快速开始

### 1. 环境检查
```bash
# 运行系统测试
./test.sh

# 如果测试失败，按照提示修复问题
```

### 2. 构建系统
```bash
# 自动安装依赖并构建
./build.sh all

# 或分步执行
./build.sh deps    # 安装依赖
./build.sh build   # 构建项目
```

### 3. 启动系统
```bash
# 启动虚拟机
virsh start win10

# 在虚拟机中启动CS2游戏

# 启动雷达系统
cd dist
./start.sh
```

### 4. 配置地图
```bash
# 创建地图占位符
python3 tools/download_maps.py --placeholder

# 使用地图校准工具 (可选)
python3 tools/map_calibrator.py
```

### 5. 访问雷达
- 本机访问: http://localhost:8080
- 局域网访问: http://你的IP:8080

## 详细配置

### 虚拟机配置

#### 创建Win10虚拟机
```bash
# 下载Windows 10 ISO
wget https://software-download.microsoft.com/download/pr/Win10_22H2_Chinese_x64.iso

# 创建虚拟机
virt-install \
  --name win10 \
  --memory 8192 \
  --vcpus 4 \
  --disk size=100,format=qcow2 \
  --cdrom Win10_22H2_Chinese_x64.iso \
  --os-variant win10 \
  --network bridge=virbr0 \
  --graphics spice \
  --video qxl
```

#### GPU直通配置 (可选)
```bash
# 1. 启用IOMMU
# 编辑 /etc/default/grub
sudo nano /etc/default/grub
# 添加: GRUB_CMDLINE_LINUX_DEFAULT="quiet splash intel_iommu=on"
# 或AMD: GRUB_CMDLINE_LINUX_DEFAULT="quiet splash amd_iommu=on"

# 2. 更新GRUB
sudo update-grub

# 3. 重启系统
sudo reboot

# 4. 检查IOMMU组
for d in /sys/kernel/iommu_groups/*/devices/*; do 
    n=${d#*/iommu_groups/*}; n=${n%%/*}
    printf 'IOMMU Group %s ' "$n"
    lspci -nns "${d##*/}"
done

# 5. 绑定GPU到vfio-pci
echo "options vfio-pci ids=10de:1234,10de:5678" | sudo tee /etc/modprobe.d/vfio.conf
sudo update-initramfs -u

# 6. 编辑虚拟机配置添加GPU
virsh edit win10
```

### 反虚拟化配置

#### 隐藏虚拟机特征
```xml
<!-- 在虚拟机XML配置中添加 -->
<domain type='kvm'>
  <features>
    <hyperv>
      <vendor_id state='on' value='1234567890ab'/>
      <spinlocks state='on' retries='8191'/>
      <vapic state='on'/>
      <time state='on'/>
      <frequencies state='on'/>
    </hyperv>
    <kvm>
      <hidden state='on'/>
    </kvm>
  </features>
  
  <cpu mode='host-passthrough' check='none'>
    <topology sockets='1' cores='4' threads='1'/>
    <feature policy='disable' name='hypervisor'/>
  </cpu>
</domain>
```

#### BIOS修改
```bash
# 使用自定义BIOS
cp /usr/share/qemu/bios-256k.bin custom-bios.bin

# 修改BIOS字符串 (需要十六进制编辑器)
# 将 "QEMU" 替换为 "DELL" 或其他厂商名称
```

### 游戏配置

#### CS2启动参数
```
-novid -nojoy -noaafonts -nohltv -noborder -disable_d3d9ex +fps_max 0 +cl_forcepreload 1
```

#### 网络配置
```bash
# 如果需要特定网络配置
# 创建桥接网络
sudo brctl addbr br0
sudo brctl addif br0 eth0
sudo ip addr add *************/24 dev br0
sudo ip link set br0 up
```

## 地图系统配置

### 地图文件管理

#### 下载地图资源
```bash
# 创建占位符地图 (用于测试)
python3 tools/download_maps.py --placeholder

# 列出已有地图
python3 tools/download_maps.py --list

# 验证地图文件
python3 tools/download_maps.py --verify
```

#### 获取真实地图图片
1. **从游戏文件提取**:
   - 使用GCFScape等工具提取CS2的雷达图片
   - 路径通常在: `csgo/resource/overviews/`

2. **从社区获取**:
   - [CS2 Radar Images](https://github.com/example/cs2-radar-images)
   - [HLTV Map Images](https://www.hltv.org/)

3. **手动制作**:
   - 使用游戏内截图
   - 通过地图编辑器导出

#### 地图文件格式
- 支持格式: PNG, JPG, SVG
- 推荐尺寸: 1024x1024 像素
- 文件命名: `地图名称.png` (如 `de_dust2.png`)

### 地图坐标校准

#### 使用校准工具
```bash
# 启动图形化校准工具
python3 tools/map_calibrator.py
```

校准工具功能:
- 加载地图图片
- 设置地图边界坐标
- 调整缩放和旋转参数
- 测试坐标转换
- 保存配置

#### 手动配置
编辑 `maps_config.json` 文件:

```json
{
  "maps": {
    "de_dust2": {
      "name": "de_dust2",
      "display_name": "Dust2",
      "image_path": "maps/de_dust2.png",
      "bounds": {
        "min_x": -2476.0,
        "max_x": 1735.0,
        "min_y": -2530.0,
        "max_y": 2042.0,
        "min_z": -103.0,
        "max_z": 390.0
      },
      "scale": 4.4,
      "rotation": 0.0,
      "offset": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
      }
    }
  }
}
```

#### 坐标系统说明
- **世界坐标**: 游戏内的3D坐标系统
- **地图坐标**: 2D雷达图片上的像素坐标
- **边界**: 地图的世界坐标范围
- **缩放**: 世界坐标到地图坐标的比例
- **旋转**: 地图相对于世界坐标的旋转角度
- **偏移**: 地图坐标的额外偏移量

### 地图自动检测

系统支持多种地图检测方式:

1. **游戏内存读取**: 从游戏进程读取当前地图名称
2. **位置检测**: 根据玩家位置自动判断地图
3. **手动切换**: 通过Web界面手动选择地图

#### 检测优先级
1. 游戏内存中的地图名称
2. 根据本地玩家位置匹配地图边界
3. 使用默认地图 (de_dust2)

### Web界面地图功能

#### 地图显示
- 实时地图背景
- 玩家位置叠加显示
- 地图信息显示
- 缩放和平移支持

#### API接口
```bash
# 获取当前地图信息
curl http://localhost:8080/api/current-map

# 获取所有地图列表
curl http://localhost:8080/api/maps

# 获取游戏数据 (包含地图坐标)
curl http://localhost:8080/api/data
```

## 偏移量更新

### 自动更新
```bash
# 使用Python工具自动更新
python3 tools/offset_updater.py

# 查看当前偏移量
python3 tools/offset_updater.py --show-current
```

### 手动更新
编辑 `src/game.rs` 中的 `GameOffsets` 结构：

```rust
impl Default for GameOffsets {
    fn default() -> Self {
        Self {
            // 更新这些偏移量
            entity_list: 0x19BBD08,    // 实体列表
            local_player: 0x1828B28,   // 本地玩家
            view_matrix: 0x19B6040,    // 视图矩阵
            
            // 玩家属性偏移
            health: 0x344,             // 血量
            team: 0x3CB,               // 队伍
            pos_x: 0x1274,             // X坐标
            pos_y: 0x1278,             // Y坐标
            pos_z: 0x127C,             // Z坐标
            // ...
        }
    }
}
```

### 偏移量来源
- [cs2-dumper](https://github.com/a2x/cs2-dumper)
- [hazedumper](https://github.com/frk1/hazedumper)
- 手动逆向分析

## 故障排除

### 常见问题

#### 1. 无法连接虚拟机
```bash
# 检查虚拟机状态
virsh list --all

# 检查libvirt服务
sudo systemctl status libvirtd

# 检查用户权限
groups | grep libvirt

# 重新添加用户到组
sudo usermod -a -G libvirt $USER
# 重新登录
```

#### 2. 找不到游戏进程
```bash
# 在虚拟机中检查进程名
# 可能的进程名: cs2.exe, csgo.exe, Counter-Strike 2.exe

# 修改配置
./radar-server --game-process "cs2.exe"
```

#### 3. 内存读取失败
```bash
# 检查MemFlow安装
memflow-cli list

# 重新安装MemFlow
cargo install memflow-cli --force

# 检查QEMU监控套接字
ls -la /var/lib/libvirt/qemu/domain-*/monitor.sock
```

#### 4. Web界面无法访问
```bash
# 检查端口占用
netstat -tlnp | grep 8080

# 检查防火墙
sudo ufw status
sudo ufw allow 8080

# 检查绑定地址
# 确保使用 0.0.0.0 而不是 127.0.0.1
```

#### 5. 偏移量过期
```bash
# 更新偏移量
python3 tools/offset_updater.py

# 手动查找新偏移量
# 使用Cheat Engine或其他内存分析工具
```

### 调试模式

#### 启用详细日志
```bash
# 启动时添加调试参数
./radar-server --debug

# 或设置环境变量
RUST_LOG=debug ./radar-server
```

#### 查看日志
```bash
# 实时查看日志
tail -f radar.log

# 过滤特定内容
grep "ERROR" radar.log
grep "memory" radar.log
```

### 性能优化

#### 调整更新频率
```rust
// 在 src/radar.rs 中修改
update_interval: Duration::from_millis(16), // 60 FPS
update_interval: Duration::from_millis(33), // 30 FPS (更省资源)
```

#### 减少内存读取
```rust
// 只读取必要的玩家数据
// 跳过距离过远的玩家
if distance > 1000.0 {
    continue;
}
```

#### 优化网络传输
```javascript
// 在Web界面中减少更新频率
setInterval(updateRadar, 50); // 20 FPS
setInterval(updateRadar, 100); // 10 FPS
```

## 安全注意事项

### 网络安全
- 只在可信网络中使用
- 考虑使用VPN或防火墙限制访问
- 定期更改默认端口

### 系统安全
- 定期更新系统和依赖
- 使用非特权用户运行
- 监控系统资源使用

### 法律合规
- 仅用于授权的测试环境
- 遵守当地法律法规
- 不在正式比赛中使用

## 高级功能

### 多游戏支持
可以扩展支持其他游戏：
1. 添加新的偏移量结构
2. 实现游戏特定的数据读取逻辑
3. 更新Web界面显示

### 数据记录
```rust
// 添加数据记录功能
// 保存玩家轨迹、击杀记录等
```

### 机器学习集成
```python
# 使用AI分析玩家行为模式
# 预测敌人位置和行动
```

## 社区支持

- GitHub Issues: 报告问题和建议
- 技术讨论: 加入相关技术社区
- 贡献代码: 提交Pull Request

---

**免责声明**: 本工具仅供学习和研究使用，请遵守相关法律法规和游戏服务条款。
