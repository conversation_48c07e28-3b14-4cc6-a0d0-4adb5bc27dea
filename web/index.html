<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CS2 KVM-DMA 雷达系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            background: #0a0a0a;
            color: #00ff00;
            font-family: 'Courier New', monospace;
            overflow: hidden;
        }
        
        .container {
            display: flex;
            height: 100vh;
        }
        
        .radar-container {
            flex: 1;
            position: relative;
            background: radial-gradient(circle, #001100 0%, #000000 100%);
        }
        
        .radar {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 600px;
            height: 600px;
            border: 2px solid #00ff00;
            border-radius: 50%;
            background: rgba(0, 255, 0, 0.05);
        }
        
        .radar-grid {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
        }
        
        .radar-line {
            position: absolute;
            background: rgba(0, 255, 0, 0.3);
        }
        
        .radar-line.horizontal {
            width: 100%;
            height: 1px;
            top: 50%;
            left: 0;
        }
        
        .radar-line.vertical {
            width: 1px;
            height: 100%;
            top: 0;
            left: 50%;
        }
        
        .radar-circle {
            position: absolute;
            border: 1px solid rgba(0, 255, 0, 0.2);
            border-radius: 50%;
        }
        
        .radar-circle.inner {
            width: 200px;
            height: 200px;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }
        
        .radar-circle.outer {
            width: 400px;
            height: 400px;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }
        
        .player-dot {
            position: absolute;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            transform: translate(-50%, -50%);
            transition: all 0.1s ease;
        }
        
        .player-dot.local {
            background: #00ff00;
            box-shadow: 0 0 10px #00ff00;
        }
        
        .player-dot.enemy {
            background: #ff0000;
            box-shadow: 0 0 10px #ff0000;
        }
        
        .player-dot.teammate {
            background: #0080ff;
            box-shadow: 0 0 10px #0080ff;
        }
        
        .player-info {
            position: absolute;
            background: rgba(0, 0, 0, 0.8);
            color: #00ff00;
            padding: 2px 4px;
            font-size: 10px;
            border-radius: 2px;
            white-space: nowrap;
            pointer-events: none;
        }
        
        .info-panel {
            width: 300px;
            background: rgba(0, 0, 0, 0.9);
            border-left: 2px solid #00ff00;
            padding: 20px;
            overflow-y: auto;
        }
        
        .status {
            margin-bottom: 20px;
        }
        
        .status-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
            font-size: 12px;
        }
        
        .status-value {
            color: #00ff00;
        }
        
        .status-value.error {
            color: #ff0000;
        }
        
        .status-value.warning {
            color: #ffff00;
        }
        
        .player-list {
            margin-top: 20px;
        }
        
        .player-item {
            background: rgba(0, 255, 0, 0.1);
            margin-bottom: 5px;
            padding: 5px;
            border-radius: 3px;
            font-size: 11px;
        }
        
        .player-item.enemy {
            background: rgba(255, 0, 0, 0.1);
            border-left: 3px solid #ff0000;
        }
        
        .player-item.teammate {
            background: rgba(0, 128, 255, 0.1);
            border-left: 3px solid #0080ff;
        }
        
        .controls {
            margin-top: 20px;
        }
        
        .control-button {
            background: rgba(0, 255, 0, 0.2);
            border: 1px solid #00ff00;
            color: #00ff00;
            padding: 5px 10px;
            margin: 2px;
            cursor: pointer;
            font-family: inherit;
            font-size: 10px;
        }
        
        .control-button:hover {
            background: rgba(0, 255, 0, 0.3);
        }
        
        .title {
            text-align: center;
            margin-bottom: 20px;
            font-size: 16px;
            color: #00ff00;
            text-shadow: 0 0 10px #00ff00;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="radar-container">
            <div class="radar" id="radar">
                <div class="radar-grid">
                    <div class="radar-line horizontal"></div>
                    <div class="radar-line vertical"></div>
                    <div class="radar-circle inner"></div>
                    <div class="radar-circle outer"></div>
                </div>
            </div>
        </div>
        
        <div class="info-panel">
            <div class="title">KVM-DMA 雷达</div>
            
            <div class="status">
                <div class="status-item">
                    <span>连接状态:</span>
                    <span class="status-value" id="connection-status">断开</span>
                </div>
                <div class="status-item">
                    <span>FPS:</span>
                    <span class="status-value" id="fps">0</span>
                </div>
                <div class="status-item">
                    <span>延迟:</span>
                    <span class="status-value" id="latency">0ms</span>
                </div>
                <div class="status-item">
                    <span>敌人数量:</span>
                    <span class="status-value" id="enemy-count">0</span>
                </div>
                <div class="status-item">
                    <span>队友数量:</span>
                    <span class="status-value" id="teammate-count">0</span>
                </div>
            </div>
            
            <div class="controls">
                <button class="control-button" onclick="toggleRadarSize()">切换大小</button>
                <button class="control-button" onclick="togglePlayerInfo()">显示信息</button>
                <button class="control-button" onclick="reconnect()">重新连接</button>
            </div>
            
            <div class="player-list" id="player-list">
                <h3>玩家列表</h3>
            </div>
        </div>
    </div>

    <script>
        class RadarSystem {
            constructor() {
                this.ws = null;
                this.gameData = null;
                this.lastUpdate = 0;
                this.fps = 0;
                this.frameCount = 0;
                this.lastFpsUpdate = Date.now();
                this.showPlayerInfo = true;
                this.radarSize = 300; // 雷达半径
                
                this.initWebSocket();
                this.startFpsCounter();
            }
            
            initWebSocket() {
                const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
                const wsUrl = `${protocol}//${window.location.host}/ws`;
                
                this.ws = new WebSocket(wsUrl);
                
                this.ws.onopen = () => {
                    console.log('WebSocket连接已建立');
                    this.updateConnectionStatus('已连接', 'status-value');
                };
                
                this.ws.onmessage = (event) => {
                    try {
                        this.gameData = JSON.parse(event.data);
                        this.updateRadar();
                        this.updateStats();
                        this.frameCount++;
                    } catch (e) {
                        console.error('解析数据失败:', e);
                    }
                };
                
                this.ws.onclose = () => {
                    console.log('WebSocket连接已断开');
                    this.updateConnectionStatus('断开', 'status-value error');
                    setTimeout(() => this.initWebSocket(), 3000);
                };
                
                this.ws.onerror = (error) => {
                    console.error('WebSocket错误:', error);
                    this.updateConnectionStatus('错误', 'status-value error');
                };
            }
            
            updateConnectionStatus(text, className) {
                const element = document.getElementById('connection-status');
                element.textContent = text;
                element.className = className;
            }
            
            updateRadar() {
                if (!this.gameData || !this.gameData.local_player) return;
                
                const radar = document.getElementById('radar');
                const localPlayer = this.gameData.local_player;
                
                // 清除现有的玩家点
                radar.querySelectorAll('.player-dot, .player-info').forEach(el => el.remove());
                
                // 绘制本地玩家
                this.drawPlayer(radar, localPlayer.position, 'local', 'YOU', localPlayer.health);
                
                // 绘制其他玩家
                Object.values(this.gameData.players).forEach(player => {
                    if (player.health > 0 && !player.dormant) {
                        const type = player.team === localPlayer.team ? 'teammate' : 'enemy';
                        this.drawPlayer(radar, player.position, type, `P${player.id}`, player.health, player.distance);
                    }
                });
            }
            
            drawPlayer(radar, worldPos, type, label, health, distance = 0) {
                const localPos = this.gameData.local_player.position;
                const radarPos = this.worldToRadar(worldPos, localPos);
                
                // 创建玩家点
                const dot = document.createElement('div');
                dot.className = `player-dot ${type}`;
                dot.style.left = `${radarPos.x}px`;
                dot.style.top = `${radarPos.y}px`;
                
                // 创建信息标签
                if (this.showPlayerInfo) {
                    const info = document.createElement('div');
                    info.className = 'player-info';
                    info.textContent = `${label} (${health}HP)`;
                    if (distance > 0) {
                        info.textContent += ` ${Math.round(distance)}m`;
                    }
                    info.style.left = `${radarPos.x + 10}px`;
                    info.style.top = `${radarPos.y - 10}px`;
                    radar.appendChild(info);
                }
                
                radar.appendChild(dot);
            }
            
            worldToRadar(worldPos, localPos) {
                const dx = worldPos.x - localPos.x;
                const dy = worldPos.y - localPos.y;
                
                // 缩放因子
                const scale = 0.5;
                
                let radarX = dx * scale;
                let radarY = -dy * scale; // Y轴翻转
                
                // 限制在雷达范围内
                const distance = Math.sqrt(radarX * radarX + radarY * radarY);
                if (distance > this.radarSize) {
                    const factor = this.radarSize / distance;
                    radarX *= factor;
                    radarY *= factor;
                }
                
                return {
                    x: 300 + radarX, // 雷达中心偏移
                    y: 300 + radarY
                };
            }
            
            updateStats() {
                if (!this.gameData) return;
                
                const enemies = Object.values(this.gameData.players).filter(p => 
                    p.health > 0 && !p.dormant && 
                    this.gameData.local_player && p.team !== this.gameData.local_player.team
                );
                
                const teammates = Object.values(this.gameData.players).filter(p => 
                    p.health > 0 && !p.dormant && 
                    this.gameData.local_player && p.team === this.gameData.local_player.team
                );
                
                document.getElementById('enemy-count').textContent = enemies.length;
                document.getElementById('teammate-count').textContent = teammates.length;
                
                // 更新玩家列表
                this.updatePlayerList(enemies, teammates);
            }
            
            updatePlayerList(enemies, teammates) {
                const playerList = document.getElementById('player-list');
                playerList.innerHTML = '<h3>玩家列表</h3>';
                
                if (enemies.length > 0) {
                    const enemyHeader = document.createElement('h4');
                    enemyHeader.textContent = '敌人';
                    enemyHeader.style.color = '#ff0000';
                    playerList.appendChild(enemyHeader);
                    
                    enemies.forEach(player => {
                        const item = document.createElement('div');
                        item.className = 'player-item enemy';
                        item.innerHTML = `
                            P${player.id} - HP: ${player.health} - 距离: ${Math.round(player.distance)}m
                        `;
                        playerList.appendChild(item);
                    });
                }
                
                if (teammates.length > 0) {
                    const teammateHeader = document.createElement('h4');
                    teammateHeader.textContent = '队友';
                    teammateHeader.style.color = '#0080ff';
                    playerList.appendChild(teammateHeader);
                    
                    teammates.forEach(player => {
                        const item = document.createElement('div');
                        item.className = 'player-item teammate';
                        item.innerHTML = `
                            P${player.id} - HP: ${player.health} - 距离: ${Math.round(player.distance)}m
                        `;
                        playerList.appendChild(item);
                    });
                }
            }
            
            startFpsCounter() {
                setInterval(() => {
                    const now = Date.now();
                    const elapsed = now - this.lastFpsUpdate;
                    
                    if (elapsed >= 1000) {
                        this.fps = Math.round((this.frameCount * 1000) / elapsed);
                        document.getElementById('fps').textContent = this.fps;
                        
                        this.frameCount = 0;
                        this.lastFpsUpdate = now;
                    }
                }, 100);
            }
        }
        
        // 全局函数
        let radarSystem;
        
        function toggleRadarSize() {
            const radar = document.getElementById('radar');
            const currentSize = parseInt(radar.style.width) || 600;
            const newSize = currentSize === 600 ? 800 : 600;
            
            radar.style.width = newSize + 'px';
            radar.style.height = newSize + 'px';
            
            radarSystem.radarSize = newSize / 2;
        }
        
        function togglePlayerInfo() {
            radarSystem.showPlayerInfo = !radarSystem.showPlayerInfo;
        }
        
        function reconnect() {
            if (radarSystem.ws) {
                radarSystem.ws.close();
            }
            radarSystem.initWebSocket();
        }
        
        // 初始化
        document.addEventListener('DOMContentLoaded', () => {
            radarSystem = new RadarSystem();
        });
    </script>
</body>
</html>
