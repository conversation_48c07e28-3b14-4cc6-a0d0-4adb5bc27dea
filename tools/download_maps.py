#!/usr/bin/env python3
"""
CS2地图资源下载工具

自动下载CS2地图的雷达图片
"""

import os
import requests
import sys
from pathlib import Path

class MapDownloader:
    def __init__(self):
        self.maps_dir = Path("web/static/maps")
        self.maps_dir.mkdir(parents=True, exist_ok=True)
        
        # CS2地图雷达图片URL (这些是示例URL，需要替换为实际的图片源)
        self.map_urls = {
            "de_dust2": "https://example.com/radar/de_dust2.png",
            "de_mirage": "https://example.com/radar/de_mirage.png", 
            "de_inferno": "https://example.com/radar/de_inferno.png",
            "de_cache": "https://example.com/radar/de_cache.png",
            "de_overpass": "https://example.com/radar/de_overpass.png",
            "de_nuke": "https://example.com/radar/de_nuke.png",
            "de_train": "https://example.com/radar/de_train.png",
            "de_vertigo": "https://example.com/radar/de_vertigo.png",
        }
    
    def download_map(self, map_name: str, url: str) -> bool:
        """下载单个地图"""
        try:
            print(f"下载地图: {map_name}")
            
            response = requests.get(url, timeout=30)
            response.raise_for_status()
            
            file_path = self.maps_dir / f"{map_name}.png"
            with open(file_path, 'wb') as f:
                f.write(response.content)
            
            print(f"✓ {map_name} 下载完成")
            return True
            
        except Exception as e:
            print(f"✗ {map_name} 下载失败: {e}")
            return False
    
    def create_placeholder_maps(self):
        """创建占位符地图图片"""
        try:
            from PIL import Image, ImageDraw, ImageFont
            
            for map_name in self.map_urls.keys():
                file_path = self.maps_dir / f"{map_name}.png"
                
                if file_path.exists():
                    continue
                
                # 创建1024x1024的占位符图片
                img = Image.new('RGB', (1024, 1024), color='#2a2a2a')
                draw = ImageDraw.Draw(img)
                
                # 绘制网格
                grid_size = 64
                for i in range(0, 1024, grid_size):
                    draw.line([(i, 0), (i, 1024)], fill='#404040', width=1)
                    draw.line([(0, i), (1024, i)], fill='#404040', width=1)
                
                # 绘制地图名称
                try:
                    font = ImageFont.truetype("/usr/share/fonts/truetype/dejavu/DejaVuSans-Bold.ttf", 48)
                except:
                    font = ImageFont.load_default()
                
                text = map_name.upper()
                bbox = draw.textbbox((0, 0), text, font=font)
                text_width = bbox[2] - bbox[0]
                text_height = bbox[3] - bbox[1]
                
                x = (1024 - text_width) // 2
                y = (1024 - text_height) // 2
                
                draw.text((x, y), text, fill='#00ff00', font=font)
                
                # 绘制边框
                draw.rectangle([(0, 0), (1023, 1023)], outline='#00ff00', width=3)
                
                img.save(file_path)
                print(f"✓ 创建占位符: {map_name}")
                
        except ImportError:
            print("警告: 未安装PIL库，无法创建占位符图片")
            print("安装命令: pip install Pillow")
            self.create_simple_placeholders()
    
    def create_simple_placeholders(self):
        """创建简单的SVG占位符"""
        for map_name in self.map_urls.keys():
            file_path = self.maps_dir / f"{map_name}.svg"
            
            if file_path.exists():
                continue
            
            svg_content = f'''<?xml version="1.0" encoding="UTF-8"?>
<svg width="1024" height="1024" xmlns="http://www.w3.org/2000/svg">
  <rect width="1024" height="1024" fill="#2a2a2a" stroke="#00ff00" stroke-width="3"/>
  <defs>
    <pattern id="grid" width="64" height="64" patternUnits="userSpaceOnUse">
      <path d="M 64 0 L 0 0 0 64" fill="none" stroke="#404040" stroke-width="1"/>
    </pattern>
  </defs>
  <rect width="1024" height="1024" fill="url(#grid)"/>
  <text x="512" y="512" font-family="Arial, sans-serif" font-size="48" 
        fill="#00ff00" text-anchor="middle" dominant-baseline="middle">
    {map_name.upper()}
  </text>
</svg>'''
            
            with open(file_path, 'w') as f:
                f.write(svg_content)
            
            print(f"✓ 创建SVG占位符: {map_name}")
    
    def download_all(self):
        """下载所有地图"""
        print("开始下载CS2地图资源...")
        
        success_count = 0
        total_count = len(self.map_urls)
        
        for map_name, url in self.map_urls.items():
            if self.download_map(map_name, url):
                success_count += 1
        
        print(f"\n下载完成: {success_count}/{total_count}")
        
        if success_count < total_count:
            print("部分地图下载失败，创建占位符...")
            self.create_placeholder_maps()
    
    def list_maps(self):
        """列出已有的地图文件"""
        print("已有地图文件:")
        
        for file_path in self.maps_dir.glob("*"):
            if file_path.is_file():
                size = file_path.stat().st_size
                print(f"  {file_path.name} ({size} bytes)")
    
    def verify_maps(self):
        """验证地图文件"""
        print("验证地图文件...")
        
        for map_name in self.map_urls.keys():
            png_path = self.maps_dir / f"{map_name}.png"
            svg_path = self.maps_dir / f"{map_name}.svg"
            
            if png_path.exists():
                print(f"✓ {map_name}.png")
            elif svg_path.exists():
                print(f"✓ {map_name}.svg (占位符)")
            else:
                print(f"✗ {map_name} 缺失")

def main():
    import argparse
    
    parser = argparse.ArgumentParser(description="CS2地图资源下载工具")
    parser.add_argument("--download", action="store_true", help="下载所有地图")
    parser.add_argument("--placeholder", action="store_true", help="创建占位符地图")
    parser.add_argument("--list", action="store_true", help="列出已有地图")
    parser.add_argument("--verify", action="store_true", help="验证地图文件")
    parser.add_argument("--map", type=str, help="下载指定地图")
    
    args = parser.parse_args()
    
    downloader = MapDownloader()
    
    if args.download:
        downloader.download_all()
    elif args.placeholder:
        downloader.create_placeholder_maps()
    elif args.list:
        downloader.list_maps()
    elif args.verify:
        downloader.verify_maps()
    elif args.map:
        if args.map in downloader.map_urls:
            downloader.download_map(args.map, downloader.map_urls[args.map])
        else:
            print(f"未知地图: {args.map}")
            print(f"可用地图: {', '.join(downloader.map_urls.keys())}")
    else:
        print("使用 --help 查看帮助")
        print("快速开始: python3 tools/download_maps.py --placeholder")

if __name__ == "__main__":
    main()
