#!/usr/bin/env python3
"""
CS2偏移量更新工具

用于从外部源获取最新的CS2游戏偏移量并更新Rust代码
"""

import json
import re
import requests
import sys
from pathlib import Path
from typing import Dict, Any

class OffsetUpdater:
    def __init__(self):
        self.offsets_sources = [
            "https://raw.githubusercontent.com/a2x/cs2-dumper/main/output/offsets.json",
            "https://raw.githubusercontent.com/frk1/hazedumper/master/csgo.json"
        ]
        self.game_rs_path = Path("src/game.rs")
        
    def fetch_offsets(self) -> Dict[str, Any]:
        """从外部源获取偏移量"""
        print("正在获取最新偏移量...")
        
        for source in self.offsets_sources:
            try:
                print(f"尝试从 {source} 获取...")
                response = requests.get(source, timeout=10)
                response.raise_for_status()
                
                data = response.json()
                print(f"成功获取偏移量数据")
                return self.parse_offsets(data)
                
            except Exception as e:
                print(f"从 {source} 获取失败: {e}")
                continue
        
        raise Exception("无法从任何源获取偏移量")
    
    def parse_offsets(self, data: Dict[str, Any]) -> Dict[str, int]:
        """解析偏移量数据"""
        offsets = {}
        
        # 根据不同的数据格式解析
        if "client.dll" in data:
            # cs2-dumper格式
            client_data = data.get("client.dll", {})
            
            # 实体相关
            if "dwEntityList" in client_data:
                offsets["entity_list"] = client_data["dwEntityList"]
            if "dwLocalPlayerPawn" in client_data:
                offsets["local_player"] = client_data["dwLocalPlayerPawn"]
            if "dwViewMatrix" in client_data:
                offsets["view_matrix"] = client_data["dwViewMatrix"]
                
        elif "signatures" in data:
            # hazedumper格式
            sigs = data.get("signatures", {})
            
            if "dwEntityList" in sigs:
                offsets["entity_list"] = int(sigs["dwEntityList"], 16)
            if "dwLocalPlayer" in sigs:
                offsets["local_player"] = int(sigs["dwLocalPlayer"], 16)
            if "dwViewMatrix" in sigs:
                offsets["view_matrix"] = int(sigs["dwViewMatrix"], 16)
        
        # 玩家实体偏移 (这些通常比较稳定)
        default_entity_offsets = {
            "health": 0x344,
            "team": 0x3CB,
            "pos_x": 0x1274,
            "pos_y": 0x1278,
            "pos_z": 0x127C,
            "view_offset_z": 0x10E8,
            "spotted": 0x93D,
            "dormant": 0xE7,
            "active_weapon": 0x12A8,
            "weapon_id": 0x1A,
            "glow_index": 0xA4,
            "crosshair_id": 0x1194,
        }
        
        offsets.update(default_entity_offsets)
        return offsets
    
    def update_rust_code(self, offsets: Dict[str, int]):
        """更新Rust代码中的偏移量"""
        print("正在更新Rust代码...")
        
        if not self.game_rs_path.exists():
            raise FileNotFoundError(f"找不到文件: {self.game_rs_path}")
        
        # 读取当前代码
        content = self.game_rs_path.read_text(encoding='utf-8')
        
        # 更新偏移量
        for name, value in offsets.items():
            pattern = rf'pub {name}: u64,.*?0x[0-9A-Fa-f]+,'
            replacement = f'pub {name}: u64, // 0x{value:X},'
            
            # 查找并替换
            if re.search(pattern, content):
                content = re.sub(pattern, replacement, content)
                print(f"更新 {name}: 0x{value:X}")
            else:
                print(f"警告: 未找到偏移量 {name}")
        
        # 添加更新时间戳
        import datetime
        timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        # 在文件开头添加更新信息
        update_comment = f"// 偏移量更新时间: {timestamp}\n// 自动生成，请勿手动修改\n\n"
        
        if "// 偏移量更新时间:" in content:
            # 替换现有的更新信息
            content = re.sub(
                r'// 偏移量更新时间:.*?\n// 自动生成，请勿手动修改\n\n',
                update_comment,
                content,
                flags=re.DOTALL
            )
        else:
            # 添加新的更新信息
            content = update_comment + content
        
        # 写回文件
        self.game_rs_path.write_text(content, encoding='utf-8')
        print(f"已更新 {self.game_rs_path}")
    
    def backup_current_offsets(self):
        """备份当前偏移量"""
        backup_path = self.game_rs_path.with_suffix('.rs.backup')
        
        if self.game_rs_path.exists():
            import shutil
            shutil.copy2(self.game_rs_path, backup_path)
            print(f"已备份当前文件到 {backup_path}")
    
    def validate_offsets(self, offsets: Dict[str, int]) -> bool:
        """验证偏移量的合理性"""
        print("验证偏移量...")
        
        # 基本检查
        required_offsets = ["entity_list", "local_player", "view_matrix"]
        for offset in required_offsets:
            if offset not in offsets:
                print(f"错误: 缺少必需的偏移量 {offset}")
                return False
            
            if offsets[offset] == 0:
                print(f"警告: 偏移量 {offset} 为0")
        
        # 范围检查
        for name, value in offsets.items():
            if value > 0x10000000:  # 256MB
                print(f"警告: 偏移量 {name} 过大: 0x{value:X}")
            elif value < 0:
                print(f"错误: 偏移量 {name} 为负数: {value}")
                return False
        
        print("偏移量验证通过")
        return True
    
    def run(self, backup: bool = True, validate: bool = True):
        """运行更新流程"""
        try:
            # 备份当前文件
            if backup:
                self.backup_current_offsets()
            
            # 获取新偏移量
            offsets = self.fetch_offsets()
            
            # 验证偏移量
            if validate and not self.validate_offsets(offsets):
                print("偏移量验证失败，取消更新")
                return False
            
            # 更新代码
            self.update_rust_code(offsets)
            
            print("\n偏移量更新完成！")
            print("请重新编译项目: cargo build --release")
            return True
            
        except Exception as e:
            print(f"更新失败: {e}")
            return False

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="CS2偏移量更新工具")
    parser.add_argument("--no-backup", action="store_true", help="不备份当前文件")
    parser.add_argument("--no-validate", action="store_true", help="跳过偏移量验证")
    parser.add_argument("--show-current", action="store_true", help="显示当前偏移量")
    
    args = parser.parse_args()
    
    updater = OffsetUpdater()
    
    if args.show_current:
        # 显示当前偏移量
        if updater.game_rs_path.exists():
            content = updater.game_rs_path.read_text()
            print("当前偏移量:")
            
            # 提取偏移量
            pattern = r'pub (\w+): u64.*?0x([0-9A-Fa-f]+)'
            matches = re.findall(pattern, content)
            
            for name, value in matches:
                print(f"  {name}: 0x{value}")
        else:
            print("找不到game.rs文件")
        return
    
    # 运行更新
    success = updater.run(
        backup=not args.no_backup,
        validate=not args.no_validate
    )
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
