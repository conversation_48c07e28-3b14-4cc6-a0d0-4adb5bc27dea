#!/usr/bin/env python3
"""
地图校准工具

帮助用户校准地图坐标和边界
"""

import json
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
from PIL import Image, ImageTk
import math

class MapCalibrator:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("CS2地图校准工具")
        self.root.geometry("1200x800")
        
        self.current_map = None
        self.map_config = {}
        self.calibration_points = []
        self.image = None
        self.photo = None
        
        self.setup_ui()
        self.load_config()
    
    def setup_ui(self):
        # 主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 左侧控制面板
        control_frame = ttk.Frame(main_frame, width=300)
        control_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 10))
        control_frame.pack_propagate(False)
        
        # 地图选择
        ttk.Label(control_frame, text="地图选择:").pack(anchor=tk.W)
        self.map_var = tk.StringVar()
        self.map_combo = ttk.Combobox(control_frame, textvariable=self.map_var, state="readonly")
        self.map_combo.pack(fill=tk.X, pady=(0, 10))
        self.map_combo.bind('<<ComboboxSelected>>', self.on_map_selected)
        
        # 加载图片按钮
        ttk.Button(control_frame, text="加载地图图片", command=self.load_image).pack(fill=tk.X, pady=(0, 10))
        
        # 坐标边界设置
        bounds_frame = ttk.LabelFrame(control_frame, text="地图边界")
        bounds_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.bounds_vars = {}
        bounds_labels = ["min_x", "max_x", "min_y", "max_y", "min_z", "max_z"]
        for i, label in enumerate(bounds_labels):
            ttk.Label(bounds_frame, text=f"{label}:").grid(row=i, column=0, sticky=tk.W, padx=5, pady=2)
            var = tk.StringVar()
            self.bounds_vars[label] = var
            entry = ttk.Entry(bounds_frame, textvariable=var, width=15)
            entry.grid(row=i, column=1, padx=5, pady=2)
        
        # 变换参数
        transform_frame = ttk.LabelFrame(control_frame, text="变换参数")
        transform_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(transform_frame, text="缩放:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
        self.scale_var = tk.StringVar(value="1.0")
        ttk.Entry(transform_frame, textvariable=self.scale_var, width=15).grid(row=0, column=1, padx=5, pady=2)
        
        ttk.Label(transform_frame, text="旋转(度):").grid(row=1, column=0, sticky=tk.W, padx=5, pady=2)
        self.rotation_var = tk.StringVar(value="0.0")
        ttk.Entry(transform_frame, textvariable=self.rotation_var, width=15).grid(row=1, column=1, padx=5, pady=2)
        
        ttk.Label(transform_frame, text="偏移X:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=2)
        self.offset_x_var = tk.StringVar(value="0.0")
        ttk.Entry(transform_frame, textvariable=self.offset_x_var, width=15).grid(row=2, column=1, padx=5, pady=2)
        
        ttk.Label(transform_frame, text="偏移Y:").grid(row=3, column=0, sticky=tk.W, padx=5, pady=2)
        self.offset_y_var = tk.StringVar(value="0.0")
        ttk.Entry(transform_frame, textvariable=self.offset_y_var, width=15).grid(row=3, column=1, padx=5, pady=2)
        
        # 校准点
        calibration_frame = ttk.LabelFrame(control_frame, text="校准点")
        calibration_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Button(calibration_frame, text="添加校准点", command=self.add_calibration_point).pack(fill=tk.X, pady=2)
        ttk.Button(calibration_frame, text="清除校准点", command=self.clear_calibration_points).pack(fill=tk.X, pady=2)
        ttk.Button(calibration_frame, text="自动校准", command=self.auto_calibrate).pack(fill=tk.X, pady=2)
        
        # 操作按钮
        ttk.Button(control_frame, text="保存配置", command=self.save_config).pack(fill=tk.X, pady=(10, 5))
        ttk.Button(control_frame, text="测试坐标", command=self.test_coordinates).pack(fill=tk.X, pady=5)
        
        # 右侧图片显示区域
        self.canvas_frame = ttk.Frame(main_frame)
        self.canvas_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
        
        self.canvas = tk.Canvas(self.canvas_frame, bg='black')
        self.canvas.pack(fill=tk.BOTH, expand=True)
        self.canvas.bind('<Button-1>', self.on_canvas_click)
        self.canvas.bind('<Motion>', self.on_canvas_motion)
        
        # 状态栏
        self.status_var = tk.StringVar(value="就绪")
        status_bar = ttk.Label(self.root, textvariable=self.status_var, relief=tk.SUNKEN)
        status_bar.pack(side=tk.BOTTOM, fill=tk.X)
    
    def load_config(self):
        """加载地图配置"""
        try:
            with open('maps_config.json', 'r', encoding='utf-8') as f:
                config = json.load(f)
                self.map_config = config.get('maps', {})
                
            # 更新地图列表
            map_names = list(self.map_config.keys())
            self.map_combo['values'] = map_names
            if map_names:
                self.map_combo.set(map_names[0])
                self.on_map_selected()
                
        except FileNotFoundError:
            messagebox.showwarning("警告", "未找到地图配置文件")
        except Exception as e:
            messagebox.showerror("错误", f"加载配置失败: {e}")
    
    def on_map_selected(self, event=None):
        """地图选择事件"""
        map_name = self.map_var.get()
        if map_name in self.map_config:
            config = self.map_config[map_name]
            
            # 更新边界值
            bounds = config.get('bounds', {})
            for key, var in self.bounds_vars.items():
                var.set(str(bounds.get(key, 0.0)))
            
            # 更新变换参数
            self.scale_var.set(str(config.get('scale', 1.0)))
            self.rotation_var.set(str(config.get('rotation', 0.0)))
            
            offset = config.get('offset', {})
            self.offset_x_var.set(str(offset.get('x', 0.0)))
            self.offset_y_var.set(str(offset.get('y', 0.0)))
            
            self.current_map = map_name
            self.status_var.set(f"已选择地图: {config.get('display_name', map_name)}")
    
    def load_image(self):
        """加载地图图片"""
        file_path = filedialog.askopenfilename(
            title="选择地图图片",
            filetypes=[("图片文件", "*.png *.jpg *.jpeg *.bmp *.gif")]
        )
        
        if file_path:
            try:
                self.image = Image.open(file_path)
                self.display_image()
                self.status_var.set(f"已加载图片: {file_path}")
            except Exception as e:
                messagebox.showerror("错误", f"加载图片失败: {e}")
    
    def display_image(self):
        """显示图片"""
        if not self.image:
            return
        
        # 获取画布尺寸
        canvas_width = self.canvas.winfo_width()
        canvas_height = self.canvas.winfo_height()
        
        if canvas_width <= 1 or canvas_height <= 1:
            self.root.after(100, self.display_image)
            return
        
        # 计算缩放比例
        img_width, img_height = self.image.size
        scale_x = canvas_width / img_width
        scale_y = canvas_height / img_height
        scale = min(scale_x, scale_y, 1.0)  # 不放大
        
        # 调整图片大小
        new_width = int(img_width * scale)
        new_height = int(img_height * scale)
        resized_image = self.image.resize((new_width, new_height), Image.Resampling.LANCZOS)
        
        # 转换为PhotoImage
        self.photo = ImageTk.PhotoImage(resized_image)
        
        # 清除画布并显示图片
        self.canvas.delete("all")
        x = (canvas_width - new_width) // 2
        y = (canvas_height - new_height) // 2
        self.canvas.create_image(x, y, anchor=tk.NW, image=self.photo)
        
        # 绘制校准点
        self.draw_calibration_points()
    
    def on_canvas_click(self, event):
        """画布点击事件"""
        if self.image:
            # 记录点击位置
            x, y = event.x, event.y
            self.status_var.set(f"点击位置: ({x}, {y})")
    
    def on_canvas_motion(self, event):
        """鼠标移动事件"""
        if self.image:
            x, y = event.x, event.y
            # 转换为图片坐标
            img_x, img_y = self.canvas_to_image_coords(x, y)
            if img_x is not None:
                self.status_var.set(f"图片坐标: ({img_x:.0f}, {img_y:.0f})")
    
    def canvas_to_image_coords(self, canvas_x, canvas_y):
        """画布坐标转图片坐标"""
        if not self.photo:
            return None, None
        
        # 获取图片在画布中的位置
        canvas_width = self.canvas.winfo_width()
        canvas_height = self.canvas.winfo_height()
        img_width = self.photo.width()
        img_height = self.photo.height()
        
        img_x_offset = (canvas_width - img_width) // 2
        img_y_offset = (canvas_height - img_height) // 2
        
        # 检查是否在图片范围内
        if (canvas_x < img_x_offset or canvas_x >= img_x_offset + img_width or
            canvas_y < img_y_offset or canvas_y >= img_y_offset + img_height):
            return None, None
        
        # 转换为图片坐标
        img_x = canvas_x - img_x_offset
        img_y = canvas_y - img_y_offset
        
        # 缩放到原始图片尺寸
        if self.image:
            scale_x = self.image.width / img_width
            scale_y = self.image.height / img_height
            img_x *= scale_x
            img_y *= scale_y
        
        return img_x, img_y
    
    def add_calibration_point(self):
        """添加校准点"""
        # 这里可以实现添加校准点的逻辑
        messagebox.showinfo("提示", "请在地图上点击已知坐标的位置")
    
    def clear_calibration_points(self):
        """清除校准点"""
        self.calibration_points.clear()
        self.display_image()
    
    def draw_calibration_points(self):
        """绘制校准点"""
        for point in self.calibration_points:
            x, y = point['canvas_pos']
            self.canvas.create_oval(x-5, y-5, x+5, y+5, fill='red', outline='white', width=2)
    
    def auto_calibrate(self):
        """自动校准"""
        messagebox.showinfo("提示", "自动校准功能正在开发中")
    
    def test_coordinates(self):
        """测试坐标转换"""
        if not self.current_map:
            messagebox.showwarning("警告", "请先选择地图")
            return
        
        # 创建测试窗口
        test_window = tk.Toplevel(self.root)
        test_window.title("坐标测试")
        test_window.geometry("400x300")
        
        ttk.Label(test_window, text="输入游戏世界坐标:").pack(pady=10)
        
        coord_frame = ttk.Frame(test_window)
        coord_frame.pack(pady=10)
        
        ttk.Label(coord_frame, text="X:").grid(row=0, column=0, padx=5)
        x_var = tk.StringVar(value="0")
        ttk.Entry(coord_frame, textvariable=x_var, width=10).grid(row=0, column=1, padx=5)
        
        ttk.Label(coord_frame, text="Y:").grid(row=0, column=2, padx=5)
        y_var = tk.StringVar(value="0")
        ttk.Entry(coord_frame, textvariable=y_var, width=10).grid(row=0, column=3, padx=5)
        
        ttk.Label(coord_frame, text="Z:").grid(row=0, column=4, padx=5)
        z_var = tk.StringVar(value="0")
        ttk.Entry(coord_frame, textvariable=z_var, width=10).grid(row=0, column=5, padx=5)
        
        result_text = tk.Text(test_window, height=10, width=50)
        result_text.pack(pady=10, padx=10, fill=tk.BOTH, expand=True)
        
        def convert_coords():
            try:
                x = float(x_var.get())
                y = float(y_var.get())
                z = float(z_var.get())
                
                # 这里实现坐标转换逻辑
                result_text.delete(1.0, tk.END)
                result_text.insert(tk.END, f"世界坐标: ({x}, {y}, {z})\n")
                result_text.insert(tk.END, f"地图坐标: 计算中...\n")
                
            except ValueError:
                messagebox.showerror("错误", "请输入有效的数字")
        
        ttk.Button(test_window, text="转换", command=convert_coords).pack(pady=10)
    
    def save_config(self):
        """保存配置"""
        if not self.current_map:
            messagebox.showwarning("警告", "请先选择地图")
            return
        
        try:
            # 更新配置
            config = self.map_config.get(self.current_map, {})
            
            # 更新边界
            bounds = {}
            for key, var in self.bounds_vars.items():
                bounds[key] = float(var.get())
            config['bounds'] = bounds
            
            # 更新变换参数
            config['scale'] = float(self.scale_var.get())
            config['rotation'] = float(self.rotation_var.get())
            config['offset'] = {
                'x': float(self.offset_x_var.get()),
                'y': float(self.offset_y_var.get()),
                'z': 0.0
            }
            
            self.map_config[self.current_map] = config
            
            # 保存到文件
            full_config = {
                'maps': self.map_config,
                'settings': {
                    'default_map_size': {'width': 1024, 'height': 1024},
                    'auto_detect': True,
                    'fallback_map': 'de_dust2'
                }
            }
            
            with open('maps_config.json', 'w', encoding='utf-8') as f:
                json.dump(full_config, f, indent=2, ensure_ascii=False)
            
            messagebox.showinfo("成功", "配置已保存")
            self.status_var.set("配置已保存")
            
        except ValueError as e:
            messagebox.showerror("错误", f"输入值无效: {e}")
        except Exception as e:
            messagebox.showerror("错误", f"保存失败: {e}")
    
    def run(self):
        """运行应用"""
        self.root.mainloop()

def main():
    app = MapCalibrator()
    app.run()

if __name__ == "__main__":
    main()
