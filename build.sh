#!/bin/bash

# KVM-DMA雷达系统构建脚本

set -e

echo "=== KVM-DMA雷达系统构建脚本 ==="

# 检查系统依赖
check_dependencies() {
    echo "检查系统依赖..."
    
    # 检查Rust
    if ! command -v cargo &> /dev/null; then
        echo "错误: 未找到Rust/Cargo，请先安装Rust"
        echo "安装命令: curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh"
        exit 1
    fi
    
    # 检查必要的系统库
    if ! pkg-config --exists libvirt; then
        echo "警告: 未找到libvirt开发库"
        echo "安装命令: sudo apt-get install libvirt-dev"
    fi
    
    echo "依赖检查完成"
}

# 安装MemFlow
install_memflow() {
    echo "安装MemFlow..."
    
    # 检查是否已安装
    if cargo install --list | grep -q memflow-cli; then
        echo "MemFlow已安装，跳过"
        return
    fi
    
    # 安装MemFlow CLI
    cargo install memflow-cli
    
    # 安装QEMU连接器
    if [ ! -d "/tmp/memflow-qemu" ]; then
        git clone https://github.com/memflow/memflow-qemu /tmp/memflow-qemu
        cd /tmp/memflow-qemu
        cargo build --release
        sudo cp target/release/libmemflow_qemu.so /usr/lib/
        cd -
    fi
    
    echo "MemFlow安装完成"
}

# 配置QEMU权限
setup_qemu_permissions() {
    echo "配置QEMU权限..."
    
    # 添加用户到libvirt组
    sudo usermod -a -G libvirt $USER
    
    # 配置QEMU监控套接字权限
    sudo mkdir -p /var/lib/libvirt/qemu
    sudo chown -R libvirt-qemu:libvirt /var/lib/libvirt/qemu
    sudo chmod 755 /var/lib/libvirt/qemu
    
    echo "QEMU权限配置完成"
    echo "注意: 需要重新登录以使组权限生效"
}

# 构建项目
build_project() {
    echo "构建项目..."
    
    # 清理之前的构建
    cargo clean
    
    # 构建发布版本
    cargo build --release
    
    # 创建输出目录
    mkdir -p dist
    
    # 复制二进制文件
    cp target/release/radar-server dist/
    
    # 复制Web文件
    cp -r web dist/

    # 复制地图配置
    cp maps_config.json dist/

    # 创建地图目录
    mkdir -p dist/web/static/maps
    cp web/static/maps/* dist/web/static/maps/ 2>/dev/null || true
    
    echo "构建完成，输出在 dist/ 目录"
}

# 创建systemd服务文件
create_service() {
    echo "创建systemd服务文件..."
    
    cat > dist/kvm-dma-radar.service << EOF
[Unit]
Description=KVM-DMA雷达系统
After=network.target libvirtd.service

[Service]
Type=simple
User=$USER
WorkingDirectory=$(pwd)/dist
ExecStart=$(pwd)/dist/radar-server --vm-name win10 --port 8080
Restart=always
RestartSec=5

[Install]
WantedBy=multi-user.target
EOF
    
    echo "服务文件已创建: dist/kvm-dma-radar.service"
    echo "安装命令: sudo cp dist/kvm-dma-radar.service /etc/systemd/system/"
    echo "启动命令: sudo systemctl enable --now kvm-dma-radar"
}

# 创建配置文件
create_config() {
    echo "创建配置文件..."
    
    cat > dist/config.toml << EOF
# KVM-DMA雷达系统配置文件

[server]
bind_address = "0.0.0.0"
port = 8080

[vm]
name = "win10"
monitor_socket = "/var/lib/libvirt/qemu/domain-win10/monitor.sock"

[game]
process_name = "cs2.exe"
update_interval_ms = 16  # ~60 FPS

[radar]
max_distance = 1000.0
scale_factor = 0.5

[logging]
level = "info"
file = "radar.log"
EOF
    
    echo "配置文件已创建: dist/config.toml"
}

# 创建启动脚本
create_launcher() {
    echo "创建启动脚本..."
    
    cat > dist/start.sh << 'EOF'
#!/bin/bash

# KVM-DMA雷达系统启动脚本

cd "$(dirname "$0")"

# 检查虚拟机状态
check_vm() {
    if ! virsh list --state-running | grep -q win10; then
        echo "错误: 虚拟机 win10 未运行"
        echo "启动命令: virsh start win10"
        exit 1
    fi
}

# 检查权限
check_permissions() {
    if ! groups | grep -q libvirt; then
        echo "错误: 当前用户不在libvirt组中"
        echo "添加命令: sudo usermod -a -G libvirt $USER"
        echo "然后重新登录"
        exit 1
    fi
}

echo "=== KVM-DMA雷达系统启动 ==="

check_permissions
check_vm

echo "启动雷达服务器..."
echo "Web界面: http://localhost:8080"
echo "按 Ctrl+C 停止服务"

./radar-server "$@"
EOF
    
    chmod +x dist/start.sh
    echo "启动脚本已创建: dist/start.sh"
}

# 主函数
main() {
    case "${1:-all}" in
        deps)
            check_dependencies
            install_memflow
            setup_qemu_permissions
            ;;
        build)
            build_project
            ;;
        service)
            create_service
            ;;
        config)
            create_config
            ;;
        launcher)
            create_launcher
            ;;
        all)
            check_dependencies
            install_memflow
            setup_qemu_permissions
            build_project
            create_service
            create_config
            create_launcher
            echo ""
            echo "=== 构建完成 ==="
            echo "1. 启动虚拟机: virsh start win10"
            echo "2. 创建地图占位符: python3 tools/download_maps.py --placeholder"
            echo "3. 运行雷达: cd dist && ./start.sh"
            echo "4. 访问Web界面: http://localhost:8080"
            echo "5. 地图校准工具: python3 tools/map_calibrator.py"
            ;;
        *)
            echo "用法: $0 [deps|build|service|config|launcher|all]"
            echo "  deps     - 安装依赖"
            echo "  build    - 构建项目"
            echo "  service  - 创建systemd服务"
            echo "  config   - 创建配置文件"
            echo "  launcher - 创建启动脚本"
            echo "  all      - 执行所有步骤 (默认)"
            exit 1
            ;;
    esac
}

main "$@"
