<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CS2地图雷达演示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            background: #0a0a0a;
            color: #00ff00;
            font-family: 'Courier New', monospace;
            overflow: hidden;
        }
        
        .container {
            display: flex;
            height: 100vh;
        }
        
        .radar-container {
            flex: 1;
            position: relative;
            background: #000000;
            overflow: hidden;
        }
        
        .radar {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 800px;
            height: 800px;
            border: 2px solid #00ff00;
            background: rgba(0, 0, 0, 0.8);
            overflow: hidden;
        }
        
        .map-background {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
            opacity: 0.8;
            background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="800" height="800"><rect width="800" height="800" fill="%23333"/><g stroke="%23555" stroke-width="1" fill="none"><path d="M0,0 L800,800 M0,800 L800,0"/><circle cx="400" cy="400" r="200"/><rect x="100" y="100" width="600" height="600" fill="none"/></g><text x="400" y="50" font-family="Arial" font-size="24" fill="%2300ff00" text-anchor="middle">DE_DUST2</text></svg>');
        }
        
        .radar-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 10;
        }
        
        .radar-grid {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            opacity: 0.3;
        }
        
        .radar-line {
            position: absolute;
            background: rgba(0, 255, 0, 0.3);
        }
        
        .radar-line.horizontal {
            width: 100%;
            height: 1px;
            top: 50%;
            left: 0;
        }
        
        .radar-line.vertical {
            width: 1px;
            height: 100%;
            top: 0;
            left: 50%;
        }
        
        .player-dot {
            position: absolute;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            transform: translate(-50%, -50%);
            transition: all 0.3s ease;
            z-index: 20;
            border: 2px solid rgba(255, 255, 255, 0.8);
        }
        
        .player-dot.local {
            background: #00ff00;
            box-shadow: 0 0 15px #00ff00;
        }
        
        .player-dot.enemy {
            background: #ff0000;
            box-shadow: 0 0 15px #ff0000;
        }
        
        .player-dot.teammate {
            background: #0080ff;
            box-shadow: 0 0 15px #0080ff;
        }
        
        .player-info {
            position: absolute;
            background: rgba(0, 0, 0, 0.9);
            color: #00ff00;
            padding: 2px 6px;
            font-size: 11px;
            border-radius: 3px;
            white-space: nowrap;
            pointer-events: none;
            z-index: 25;
            border: 1px solid rgba(0, 255, 0, 0.5);
        }
        
        .map-info {
            position: absolute;
            top: 10px;
            left: 10px;
            background: rgba(0, 0, 0, 0.8);
            color: #00ff00;
            padding: 5px 10px;
            border-radius: 5px;
            font-size: 14px;
            z-index: 30;
        }
        
        .info-panel {
            width: 300px;
            background: rgba(0, 0, 0, 0.9);
            border-left: 2px solid #00ff00;
            padding: 20px;
            overflow-y: auto;
        }
        
        .status {
            margin-bottom: 20px;
        }
        
        .status-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
            font-size: 12px;
        }
        
        .status-value {
            color: #00ff00;
        }
        
        .controls {
            margin-top: 20px;
        }
        
        .control-button {
            background: rgba(0, 255, 0, 0.2);
            border: 1px solid #00ff00;
            color: #00ff00;
            padding: 5px 10px;
            margin: 2px;
            cursor: pointer;
            font-family: inherit;
            font-size: 10px;
            width: 100%;
        }
        
        .control-button:hover {
            background: rgba(0, 255, 0, 0.3);
        }
        
        .title {
            text-align: center;
            margin-bottom: 20px;
            font-size: 16px;
            color: #00ff00;
            text-shadow: 0 0 10px #00ff00;
        }
        
        .demo-notice {
            background: rgba(255, 255, 0, 0.1);
            border: 1px solid #ffff00;
            color: #ffff00;
            padding: 10px;
            margin-bottom: 20px;
            border-radius: 5px;
            font-size: 11px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="radar-container">
            <div class="radar" id="radar">
                <div class="map-background" id="map-background"></div>
                <div class="radar-overlay">
                    <div class="radar-grid">
                        <div class="radar-line horizontal"></div>
                        <div class="radar-line vertical"></div>
                    </div>
                </div>
                <div class="map-info" id="map-info">Dust2 - 演示模式</div>
            </div>
        </div>
        
        <div class="info-panel">
            <div class="title">KVM-DMA 雷达演示</div>
            
            <div class="demo-notice">
                这是地图雷达系统的演示版本。<br>
                实际版本会从游戏内存读取真实数据。
            </div>
            
            <div class="status">
                <div class="status-item">
                    <span>演示状态:</span>
                    <span class="status-value" id="demo-status">运行中</span>
                </div>
                <div class="status-item">
                    <span>模拟FPS:</span>
                    <span class="status-value" id="fps">60</span>
                </div>
                <div class="status-item">
                    <span>敌人数量:</span>
                    <span class="status-value" id="enemy-count">0</span>
                </div>
                <div class="status-item">
                    <span>队友数量:</span>
                    <span class="status-value" id="teammate-count">0</span>
                </div>
            </div>
            
            <div class="controls">
                <button class="control-button" onclick="toggleMap()">切换地图</button>
                <button class="control-button" onclick="togglePlayerInfo()">显示信息</button>
                <button class="control-button" onclick="addRandomPlayers()">添加玩家</button>
                <button class="control-button" onclick="clearPlayers()">清除玩家</button>
                <button class="control-button" onclick="toggleAnimation()">动画效果</button>
            </div>
            
            <div style="margin-top: 20px; font-size: 10px; color: #666;">
                <h4>功能说明:</h4>
                <ul style="margin-left: 15px;">
                    <li>绿色点: 本地玩家</li>
                    <li>红色点: 敌方玩家</li>
                    <li>蓝色点: 队友</li>
                    <li>实时位置更新</li>
                    <li>地图背景叠加</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        class RadarDemo {
            constructor() {
                this.players = [];
                this.showPlayerInfo = true;
                this.animationEnabled = true;
                this.currentMapIndex = 0;
                this.maps = [
                    { name: 'Dust2', background: 'data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="800" height="800"><rect width="800" height="800" fill="%23444"/><g stroke="%23666" stroke-width="2" fill="none"><rect x="50" y="50" width="700" height="700"/><path d="M50,400 L750,400 M400,50 L400,750"/></g><text x="400" y="40" font-family="Arial" font-size="20" fill="%2300ff00" text-anchor="middle">DUST2</text></svg>' },
                    { name: 'Mirage', background: 'data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="800" height="800"><rect width="800" height="800" fill="%23335533"/><g stroke="%23557755" stroke-width="2" fill="none"><circle cx="400" cy="400" r="300"/><path d="M100,400 L700,400 M400,100 L400,700"/></g><text x="400" y="40" font-family="Arial" font-size="20" fill="%2300ff00" text-anchor="middle">MIRAGE</text></svg>' },
                    { name: 'Inferno', background: 'data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="800" height="800"><rect width="800" height="800" fill="%23553333"/><g stroke="%23775555" stroke-width="2" fill="none"><polygon points="400,100 700,400 400,700 100,400"/><path d="M100,400 L700,400 M400,100 L400,700"/></g><text x="400" y="40" font-family="Arial" font-size="20" fill="%2300ff00" text-anchor="middle">INFERNO</text></svg>' }
                ];
                
                this.init();
            }
            
            init() {
                // 添加本地玩家
                this.addPlayer(400, 400, 'local', 'YOU', 100);
                
                // 开始动画循环
                this.startAnimation();
                
                // 添加一些初始玩家
                this.addRandomPlayers();
            }
            
            addPlayer(x, y, type, name, health) {
                const player = {
                    x: x,
                    y: y,
                    type: type,
                    name: name,
                    health: health,
                    id: Date.now() + Math.random()
                };
                this.players.push(player);
                this.updateDisplay();
            }
            
            updateDisplay() {
                const radar = document.getElementById('radar');
                
                // 清除现有玩家点
                radar.querySelectorAll('.player-dot, .player-info').forEach(el => el.remove());
                
                let enemyCount = 0;
                let teammateCount = 0;
                
                // 绘制所有玩家
                this.players.forEach(player => {
                    this.drawPlayer(radar, player);
                    
                    if (player.type === 'enemy') enemyCount++;
                    else if (player.type === 'teammate') teammateCount++;
                });
                
                // 更新统计
                document.getElementById('enemy-count').textContent = enemyCount;
                document.getElementById('teammate-count').textContent = teammateCount;
            }
            
            drawPlayer(radar, player) {
                // 创建玩家点
                const dot = document.createElement('div');
                dot.className = `player-dot ${player.type}`;
                dot.style.left = `${player.x}px`;
                dot.style.top = `${player.y}px`;
                
                // 创建信息标签
                if (this.showPlayerInfo) {
                    const info = document.createElement('div');
                    info.className = 'player-info';
                    info.textContent = `${player.name} (${player.health}HP)`;
                    info.style.left = `${player.x + 15}px`;
                    info.style.top = `${player.y - 15}px`;
                    radar.appendChild(info);
                }
                
                radar.appendChild(dot);
            }
            
            startAnimation() {
                if (!this.animationEnabled) return;
                
                setInterval(() => {
                    // 随机移动玩家
                    this.players.forEach(player => {
                        if (player.type !== 'local') {
                            player.x += (Math.random() - 0.5) * 10;
                            player.y += (Math.random() - 0.5) * 10;
                            
                            // 保持在地图范围内
                            player.x = Math.max(50, Math.min(750, player.x));
                            player.y = Math.max(50, Math.min(750, player.y));
                        }
                    });
                    
                    this.updateDisplay();
                }, 100);
            }
        }
        
        let radarDemo;
        
        function toggleMap() {
            radarDemo.currentMapIndex = (radarDemo.currentMapIndex + 1) % radarDemo.maps.length;
            const map = radarDemo.maps[radarDemo.currentMapIndex];
            
            document.getElementById('map-background').style.backgroundImage = `url('${map.background}')`;
            document.getElementById('map-info').textContent = `${map.name} - 演示模式`;
        }
        
        function togglePlayerInfo() {
            radarDemo.showPlayerInfo = !radarDemo.showPlayerInfo;
            radarDemo.updateDisplay();
        }
        
        function addRandomPlayers() {
            const types = ['enemy', 'teammate'];
            const names = ['Alpha', 'Bravo', 'Charlie', 'Delta', 'Echo'];
            
            for (let i = 0; i < 3; i++) {
                const type = types[Math.floor(Math.random() * types.length)];
                const name = names[Math.floor(Math.random() * names.length)];
                const x = 100 + Math.random() * 600;
                const y = 100 + Math.random() * 600;
                const health = 50 + Math.random() * 50;
                
                radarDemo.addPlayer(x, y, type, name, Math.floor(health));
            }
        }
        
        function clearPlayers() {
            radarDemo.players = radarDemo.players.filter(p => p.type === 'local');
            radarDemo.updateDisplay();
        }
        
        function toggleAnimation() {
            radarDemo.animationEnabled = !radarDemo.animationEnabled;
            if (radarDemo.animationEnabled) {
                radarDemo.startAnimation();
            }
        }
        
        // 初始化演示
        document.addEventListener('DOMContentLoaded', () => {
            radarDemo = new RadarDemo();
        });
    </script>
</body>
</html>
