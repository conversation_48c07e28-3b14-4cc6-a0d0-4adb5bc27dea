#!/bin/bash

# KVM-DMA雷达系统测试脚本

set -e

echo "=== KVM-DMA雷达系统测试 ==="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 测试结果统计
TESTS_PASSED=0
TESTS_FAILED=0

# 打印测试结果
print_result() {
    if [ $1 -eq 0 ]; then
        echo -e "${GREEN}✓ $2${NC}"
        ((TESTS_PASSED++))
    else
        echo -e "${RED}✗ $2${NC}"
        ((TESTS_FAILED++))
    fi
}

# 测试系统依赖
test_dependencies() {
    echo "测试系统依赖..."
    
    # 测试Rust
    if command -v cargo &> /dev/null; then
        print_result 0 "Rust/Cargo已安装"
    else
        print_result 1 "Rust/Cargo未安装"
    fi
    
    # 测试KVM
    if command -v virsh &> /dev/null; then
        print_result 0 "libvirt已安装"
    else
        print_result 1 "libvirt未安装"
    fi
    
    # 测试开发库
    if pkg-config --exists libvirt; then
        print_result 0 "libvirt开发库已安装"
    else
        print_result 1 "libvirt开发库未安装"
    fi
    
    # 测试用户权限
    if groups | grep -q libvirt; then
        print_result 0 "用户在libvirt组中"
    else
        print_result 1 "用户不在libvirt组中"
    fi
}

# 测试虚拟机状态
test_vm_status() {
    echo "测试虚拟机状态..."
    
    # 检查虚拟机是否存在
    if virsh list --all | grep -q win10; then
        print_result 0 "虚拟机win10存在"
        
        # 检查虚拟机是否运行
        if virsh list --state-running | grep -q win10; then
            print_result 0 "虚拟机win10正在运行"
        else
            print_result 1 "虚拟机win10未运行"
            echo -e "${YELLOW}提示: 使用 'virsh start win10' 启动虚拟机${NC}"
        fi
    else
        print_result 1 "虚拟机win10不存在"
        echo -e "${YELLOW}提示: 需要先创建win10虚拟机${NC}"
    fi
}

# 测试项目构建
test_build() {
    echo "测试项目构建..."
    
    # 检查Cargo.toml
    if [ -f "Cargo.toml" ]; then
        print_result 0 "Cargo.toml存在"
    else
        print_result 1 "Cargo.toml不存在"
        return
    fi
    
    # 测试依赖检查
    if cargo check &> /dev/null; then
        print_result 0 "依赖检查通过"
    else
        print_result 1 "依赖检查失败"
        echo -e "${YELLOW}运行 'cargo check' 查看详细错误${NC}"
    fi
    
    # 测试编译
    echo "正在编译项目..."
    if cargo build --release &> build.log; then
        print_result 0 "项目编译成功"
    else
        print_result 1 "项目编译失败"
        echo -e "${YELLOW}查看 build.log 了解详细错误${NC}"
    fi
}

# 测试MemFlow安装
test_memflow() {
    echo "测试MemFlow..."
    
    # 检查MemFlow CLI
    if command -v memflow-cli &> /dev/null; then
        print_result 0 "MemFlow CLI已安装"
        
        # 测试连接器列表
        if memflow-cli list &> /dev/null; then
            print_result 0 "MemFlow连接器可用"
        else
            print_result 1 "MemFlow连接器不可用"
        fi
    else
        print_result 1 "MemFlow CLI未安装"
        echo -e "${YELLOW}运行 './build.sh deps' 安装MemFlow${NC}"
    fi
}

# 测试网络连接
test_network() {
    echo "测试网络配置..."
    
    # 检查端口是否被占用
    if netstat -ln | grep -q ":8080"; then
        print_result 1 "端口8080已被占用"
    else
        print_result 0 "端口8080可用"
    fi
    
    # 检查防火墙状态
    if command -v ufw &> /dev/null; then
        if ufw status | grep -q "Status: active"; then
            if ufw status | grep -q "8080"; then
                print_result 0 "防火墙已配置端口8080"
            else
                print_result 1 "防火墙未配置端口8080"
                echo -e "${YELLOW}运行 'sudo ufw allow 8080' 开放端口${NC}"
            fi
        else
            print_result 0 "防火墙未启用"
        fi
    fi
}

# 测试Web文件
test_web_files() {
    echo "测试Web文件..."
    
    if [ -f "web/index.html" ]; then
        print_result 0 "Web界面文件存在"
    else
        print_result 1 "Web界面文件不存在"
    fi
    
    # 检查HTML语法
    if command -v tidy &> /dev/null; then
        if tidy -q -e web/index.html &> /dev/null; then
            print_result 0 "HTML语法正确"
        else
            print_result 1 "HTML语法错误"
        fi
    fi
}

# 模拟测试
test_simulation() {
    echo "运行模拟测试..."
    
    # 创建测试配置
    cat > test_config.toml << EOF
[server]
bind_address = "127.0.0.1"
port = 8081

[vm]
name = "test-vm"

[game]
process_name = "test.exe"
update_interval_ms = 100
EOF
    
    # 测试配置解析
    if [ -f "test_config.toml" ]; then
        print_result 0 "测试配置创建成功"
        rm test_config.toml
    else
        print_result 1 "测试配置创建失败"
    fi
}

# 性能测试
test_performance() {
    echo "性能测试..."
    
    # 检查系统资源
    local mem_total=$(free -m | awk 'NR==2{print $2}')
    if [ $mem_total -gt 8192 ]; then
        print_result 0 "内存充足 (${mem_total}MB)"
    else
        print_result 1 "内存不足 (${mem_total}MB < 8192MB)"
    fi
    
    # 检查CPU核心数
    local cpu_cores=$(nproc)
    if [ $cpu_cores -ge 4 ]; then
        print_result 0 "CPU核心充足 (${cpu_cores}核)"
    else
        print_result 1 "CPU核心不足 (${cpu_cores}核 < 4核)"
    fi
    
    # 检查磁盘空间
    local disk_free=$(df -BG . | awk 'NR==2{print $4}' | sed 's/G//')
    if [ $disk_free -gt 10 ]; then
        print_result 0 "磁盘空间充足 (${disk_free}GB)"
    else
        print_result 1 "磁盘空间不足 (${disk_free}GB < 10GB)"
    fi
}

# 生成测试报告
generate_report() {
    echo ""
    echo "=== 测试报告 ==="
    echo -e "通过测试: ${GREEN}$TESTS_PASSED${NC}"
    echo -e "失败测试: ${RED}$TESTS_FAILED${NC}"
    echo -e "总计测试: $((TESTS_PASSED + TESTS_FAILED))"
    
    if [ $TESTS_FAILED -eq 0 ]; then
        echo -e "${GREEN}所有测试通过！系统准备就绪。${NC}"
        echo ""
        echo "下一步操作:"
        echo "1. 启动虚拟机: virsh start win10"
        echo "2. 在虚拟机中启动CS2"
        echo "3. 运行雷达: ./dist/start.sh"
        echo "4. 访问Web界面: http://localhost:8080"
    else
        echo -e "${RED}存在失败的测试，请检查并修复问题。${NC}"
        echo ""
        echo "常见解决方案:"
        echo "- 安装依赖: ./build.sh deps"
        echo "- 构建项目: ./build.sh build"
        echo "- 检查权限: sudo usermod -a -G libvirt \$USER"
        echo "- 重新登录以使权限生效"
    fi
}

# 主函数
main() {
    case "${1:-all}" in
        deps)
            test_dependencies
            ;;
        vm)
            test_vm_status
            ;;
        build)
            test_build
            ;;
        memflow)
            test_memflow
            ;;
        network)
            test_network
            ;;
        web)
            test_web_files
            ;;
        sim)
            test_simulation
            ;;
        perf)
            test_performance
            ;;
        all)
            test_dependencies
            test_vm_status
            test_memflow
            test_build
            test_web_files
            test_network
            test_performance
            test_simulation
            generate_report
            ;;
        *)
            echo "用法: $0 [deps|vm|build|memflow|network|web|sim|perf|all]"
            echo "  deps     - 测试系统依赖"
            echo "  vm       - 测试虚拟机状态"
            echo "  build    - 测试项目构建"
            echo "  memflow  - 测试MemFlow"
            echo "  network  - 测试网络配置"
            echo "  web      - 测试Web文件"
            echo "  sim      - 运行模拟测试"
            echo "  perf     - 性能测试"
            echo "  all      - 运行所有测试 (默认)"
            exit 1
            ;;
    esac
    
    if [ "$1" != "all" ]; then
        generate_report
    fi
}

main "$@"
