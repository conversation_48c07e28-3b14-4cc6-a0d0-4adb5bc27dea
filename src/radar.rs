use anyhow::{Result, anyhow};
use log::{info, debug, warn, error};
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::sync::RwLock;
use tokio::time;

use crate::memory::MemoryReader;
use crate::game::{GameData, GameReader, Vector3};
use crate::maps::MapManager;

pub struct RadarServer {
    memory_reader: Arc<RwLock<MemoryReader>>,
    game_data: Arc<RwLock<GameData>>,
    map_manager: Arc<RwLock<MapManager>>,
    update_interval: Duration,
}

impl RadarServer {
    pub fn new(
        memory_reader: Arc<RwLock<MemoryReader>>,
        game_data: Arc<RwLock<GameData>>,
        map_manager: Arc<RwLock<MapManager>>,
    ) -> Self {
        Self {
            memory_reader,
            game_data,
            map_manager,
            update_interval: Duration::from_millis(16), // ~60 FPS
        }
    }
    
    pub async fn run(&self) -> Result<()> {
        info!("启动雷达数据采集服务...");
        
        let mut game_reader = None;
        let mut last_update = Instant::now();
        let mut error_count = 0;
        const MAX_ERRORS: u32 = 10;
        
        loop {
            let start_time = Instant::now();
            
            match self.update_game_data(&mut game_reader).await {
                Ok(_) => {
                    error_count = 0;
                    let update_time = start_time.elapsed();
                    
                    if update_time > Duration::from_millis(50) {
                        warn!("数据更新耗时过长: {:?}", update_time);
                    }
                    
                    debug!("数据更新完成，耗时: {:?}", update_time);
                }
                Err(e) => {
                    error_count += 1;
                    error!("数据更新失败 ({}/{}): {}", error_count, MAX_ERRORS, e);
                    
                    if error_count >= MAX_ERRORS {
                        error!("连续错误次数过多，尝试重新连接...");
                        game_reader = None;
                        error_count = 0;
                        
                        // 等待一段时间再重试
                        time::sleep(Duration::from_secs(5)).await;
                        continue;
                    }
                }
            }
            
            // 控制更新频率
            let elapsed = last_update.elapsed();
            if elapsed < self.update_interval {
                time::sleep(self.update_interval - elapsed).await;
            }
            last_update = Instant::now();
        }
    }
    
    async fn update_game_data(&self, game_reader: &mut Option<GameReader>) -> Result<()> {
        let mut memory = self.memory_reader.write().await;
        
        // 检查进程是否存活
        if !memory.is_process_alive() {
            warn!("目标进程已退出，尝试重新连接...");
            memory.reconnect("cs2.exe").await?;
            *game_reader = None;
        }
        
        // 初始化游戏读取器
        if game_reader.is_none() {
            info!("初始化游戏数据读取器...");
            *game_reader = Some(GameReader::new(&mut *memory)?);
        }
        
        let reader = game_reader.as_ref().unwrap();
        
        // 读取游戏数据
        let mut new_game_data = reader.read_game_data(&mut *memory)?;

        // 更新地图管理器
        self.update_map_manager(&new_game_data).await;

        // 计算屏幕坐标和地图坐标
        self.calculate_screen_positions(&mut new_game_data);
        self.calculate_map_positions(&mut new_game_data).await;

        // 更新共享数据
        {
            let mut game_data = self.game_data.write().await;
            *game_data = new_game_data;
        }
        
        Ok(())
    }
    
    async fn update_map_manager(&self, game_data: &GameData) {
        if let Some(map_name) = &game_data.current_map {
            let mut map_manager = self.map_manager.write().await;

            // 如果检测到新地图，切换地图
            if map_manager.current_map().map(|m| &m.name) != Some(map_name) {
                if let Err(e) = map_manager.set_current_map(map_name) {
                    warn!("切换地图失败: {}", e);
                } else {
                    info!("切换到地图: {}", map_name);
                }
            }
        } else if let Some(local_player) = &game_data.local_player {
            // 如果无法从游戏数据获取地图名，尝试根据位置检测
            let mut map_manager = self.map_manager.write().await;
            if let Some(detected_map) = map_manager.detect_map(&local_player.position) {
                info!("根据位置检测到地图: {}", detected_map.name);
            }
        }
    }

    fn calculate_screen_positions(&self, game_data: &mut GameData) {
        let view_matrix = &game_data.view_matrix;

        for player in game_data.players.values_mut() {
            // 世界坐标转屏幕坐标
            if let Some(screen_pos) = world_to_screen(&player.position, view_matrix) {
                player.screen_pos = Some(screen_pos);
            }
        }
    }

    async fn calculate_map_positions(&self, game_data: &mut GameData) {
        let map_manager = self.map_manager.read().await;

        // 地图尺寸 (可以从配置中读取)
        let map_size = (1024, 1024);

        for player in game_data.players.values_mut() {
            // 世界坐标转地图坐标
            if let Some(map_pos) = map_manager.world_to_map(&player.position, map_size) {
                player.map_pos = Some(map_pos);
            }
        }
    }
}

/// 世界坐标转屏幕坐标
fn world_to_screen(world_pos: &Vector3, view_matrix: &[[f32; 4]; 4]) -> Option<Vector3> {
    // 应用视图矩阵变换
    let clip_x = world_pos.x * view_matrix[0][0] + 
                 world_pos.y * view_matrix[0][1] + 
                 world_pos.z * view_matrix[0][2] + 
                 view_matrix[0][3];
    
    let clip_y = world_pos.x * view_matrix[1][0] + 
                 world_pos.y * view_matrix[1][1] + 
                 world_pos.z * view_matrix[1][2] + 
                 view_matrix[1][3];
    
    let clip_w = world_pos.x * view_matrix[3][0] + 
                 world_pos.y * view_matrix[3][1] + 
                 world_pos.z * view_matrix[3][2] + 
                 view_matrix[3][3];
    
    // 检查是否在视野内
    if clip_w < 0.1 {
        return None;
    }
    
    // 标准化设备坐标
    let ndc_x = clip_x / clip_w;
    let ndc_y = clip_y / clip_w;
    
    // 转换为屏幕坐标 (假设1920x1080分辨率)
    const SCREEN_WIDTH: f32 = 1920.0;
    const SCREEN_HEIGHT: f32 = 1080.0;
    
    let screen_x = (ndc_x + 1.0) * 0.5 * SCREEN_WIDTH;
    let screen_y = (1.0 - ndc_y) * 0.5 * SCREEN_HEIGHT;
    
    // 检查是否在屏幕范围内
    if screen_x >= 0.0 && screen_x <= SCREEN_WIDTH && 
       screen_y >= 0.0 && screen_y <= SCREEN_HEIGHT {
        Some(Vector3::new(screen_x, screen_y, clip_w))
    } else {
        None
    }
}

/// 计算雷达坐标
pub fn world_to_radar(world_pos: &Vector3, local_pos: &Vector3, radar_size: f32) -> Vector3 {
    let dx = world_pos.x - local_pos.x;
    let dy = world_pos.y - local_pos.y;
    
    // 缩放因子 (可调整)
    let scale = 0.1;
    
    let radar_x = dx * scale;
    let radar_y = dy * scale;
    
    // 限制在雷达范围内
    let distance = (radar_x * radar_x + radar_y * radar_y).sqrt();
    if distance > radar_size {
        let factor = radar_size / distance;
        Vector3::new(radar_x * factor, radar_y * factor, 0.0)
    } else {
        Vector3::new(radar_x, radar_y, 0.0)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_world_to_screen() {
        let view_matrix = [
            [1.0, 0.0, 0.0, 0.0],
            [0.0, 1.0, 0.0, 0.0],
            [0.0, 0.0, 1.0, 0.0],
            [0.0, 0.0, 0.0, 1.0],
        ];
        
        let world_pos = Vector3::new(0.0, 0.0, 10.0);
        let screen_pos = world_to_screen(&world_pos, &view_matrix);
        
        assert!(screen_pos.is_some());
    }
    
    #[test]
    fn test_world_to_radar() {
        let world_pos = Vector3::new(100.0, 200.0, 0.0);
        let local_pos = Vector3::new(0.0, 0.0, 0.0);
        let radar_pos = world_to_radar(&world_pos, &local_pos, 50.0);
        
        assert!(radar_pos.x.abs() <= 50.0);
        assert!(radar_pos.y.abs() <= 50.0);
    }
}
