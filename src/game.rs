use anyhow::{Result, anyhow};
use log::{debug, warn};
use memflow::prelude::*;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use crate::memory::MemoryReader;

// CS2游戏偏移量 - 这些需要根据实际游戏版本更新
pub struct GameOffsets {
    // 客户端模块偏移
    pub client_dll: &'static str,
    pub engine_dll: &'static str,
    
    // 实体相关偏移
    pub entity_list: u64,
    pub local_player: u64,
    pub view_matrix: u64,
    
    // 玩家实体偏移
    pub health: u64,
    pub team: u64,
    pub pos_x: u64,
    pub pos_y: u64,
    pub pos_z: u64,
    pub view_offset_z: u64,
    pub spotted: u64,
    pub dormant: u64,
    
    // 武器相关
    pub active_weapon: u64,
    pub weapon_id: u64,
    
    // 其他
    pub glow_index: u64,
    pub crosshair_id: u64,
}

impl Default for GameOffsets {
    fn default() -> Self {
        Self {
            // 模块名称
            client_dll: "client.dll",
            engine_dll: "engine2.dll",
            
            // 基础偏移 - 需要根据实际游戏版本调整
            entity_list: 0x19BBD08,
            local_player: 0x1828B28,
            view_matrix: 0x19B6040,
            
            // 实体偏移
            health: 0x344,
            team: 0x3CB,
            pos_x: 0x1274,
            pos_y: 0x1278,
            pos_z: 0x127C,
            view_offset_z: 0x10E8,
            spotted: 0x93D,
            dormant: 0xE7,
            
            // 武器
            active_weapon: 0x12A8,
            weapon_id: 0x1A,
            
            // 其他
            glow_index: 0xA4,
            crosshair_id: 0x1194,
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Vector3 {
    pub x: f32,
    pub y: f32,
    pub z: f32,
}

impl Vector3 {
    pub fn new(x: f32, y: f32, z: f32) -> Self {
        Self { x, y, z }
    }
    
    pub fn distance_to(&self, other: &Vector3) -> f32 {
        let dx = self.x - other.x;
        let dy = self.y - other.y;
        let dz = self.z - other.z;
        (dx * dx + dy * dy + dz * dz).sqrt()
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Player {
    pub id: u32,
    pub health: i32,
    pub team: i32,
    pub position: Vector3,
    pub head_position: Vector3,
    pub spotted: bool,
    pub dormant: bool,
    pub weapon_id: i32,
    pub distance: f32,
    pub screen_pos: Option<Vector3>,
}

impl Player {
    pub fn is_valid(&self) -> bool {
        self.health > 0 && self.health <= 100 && !self.dormant
    }
    
    pub fn is_enemy(&self, local_team: i32) -> bool {
        self.team != local_team && self.team > 0
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LocalPlayer {
    pub health: i32,
    pub team: i32,
    pub position: Vector3,
    pub view_angles: Vector3,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GameData {
    pub local_player: Option<LocalPlayer>,
    pub players: HashMap<u32, Player>,
    pub view_matrix: [[f32; 4]; 4],
    pub timestamp: u64,
}

impl GameData {
    pub fn new() -> Self {
        Self {
            local_player: None,
            players: HashMap::new(),
            view_matrix: [[0.0; 4]; 4],
            timestamp: 0,
        }
    }
    
    pub fn get_enemies(&self) -> Vec<&Player> {
        if let Some(local) = &self.local_player {
            self.players
                .values()
                .filter(|p| p.is_valid() && p.is_enemy(local.team))
                .collect()
        } else {
            Vec::new()
        }
    }
    
    pub fn get_teammates(&self) -> Vec<&Player> {
        if let Some(local) = &self.local_player {
            self.players
                .values()
                .filter(|p| p.is_valid() && p.team == local.team)
                .collect()
        } else {
            Vec::new()
        }
    }
}

pub struct GameReader {
    offsets: GameOffsets,
    client_base: Address,
    engine_base: Address,
}

impl GameReader {
    pub fn new(memory: &mut MemoryReader) -> Result<Self> {
        let offsets = GameOffsets::default();
        
        let client_base = memory.get_module_base(offsets.client_dll)?;
        let engine_base = memory.get_module_base(offsets.engine_dll)?;
        
        debug!("Client.dll 基址: 0x{:x}", client_base);
        debug!("Engine2.dll 基址: 0x{:x}", engine_base);
        
        Ok(Self {
            offsets,
            client_base,
            engine_base,
        })
    }
    
    pub fn read_game_data(&self, memory: &mut MemoryReader) -> Result<GameData> {
        let mut game_data = GameData::new();
        game_data.timestamp = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap()
            .as_millis() as u64;
        
        // 读取视图矩阵
        game_data.view_matrix = self.read_view_matrix(memory)?;
        
        // 读取本地玩家
        game_data.local_player = self.read_local_player(memory).ok();
        
        // 读取所有玩家
        game_data.players = self.read_all_players(memory, &game_data.local_player)?;
        
        Ok(game_data)
    }
    
    fn read_view_matrix(&self, memory: &mut MemoryReader) -> Result<[[f32; 4]; 4]> {
        let matrix_addr = self.client_base + self.offsets.view_matrix;
        let mut matrix = [[0.0f32; 4]; 4];
        
        for i in 0..4 {
            for j in 0..4 {
                let addr = matrix_addr + (i * 4 + j) * 4;
                matrix[i][j] = memory.read_memory::<f32>(addr)?;
            }
        }
        
        Ok(matrix)
    }
    
    fn read_local_player(&self, memory: &mut MemoryReader) -> Result<LocalPlayer> {
        let local_player_addr = memory.read_memory::<u64>(self.client_base + self.offsets.local_player)?;
        
        if local_player_addr == 0 {
            return Err(anyhow!("本地玩家地址无效"));
        }
        
        let health = memory.read_memory::<i32>(local_player_addr + self.offsets.health)?;
        let team = memory.read_memory::<i32>(local_player_addr + self.offsets.team)?;
        
        let pos_x = memory.read_memory::<f32>(local_player_addr + self.offsets.pos_x)?;
        let pos_y = memory.read_memory::<f32>(local_player_addr + self.offsets.pos_y)?;
        let pos_z = memory.read_memory::<f32>(local_player_addr + self.offsets.pos_z)?;
        
        Ok(LocalPlayer {
            health,
            team,
            position: Vector3::new(pos_x, pos_y, pos_z),
            view_angles: Vector3::new(0.0, 0.0, 0.0), // 暂时不读取视角
        })
    }
    
    fn read_all_players(&self, memory: &mut MemoryReader, local_player: &Option<LocalPlayer>) -> Result<HashMap<u32, Player>> {
        let mut players = HashMap::new();
        let entity_list_addr = self.client_base + self.offsets.entity_list;
        
        // 读取最多64个玩家实体
        for i in 1..=64u32 {
            if let Ok(player) = self.read_player(memory, entity_list_addr, i, local_player) {
                if player.is_valid() {
                    players.insert(i, player);
                }
            }
        }
        
        Ok(players)
    }
    
    fn read_player(&self, memory: &mut MemoryReader, entity_list_addr: u64, index: u32, local_player: &Option<LocalPlayer>) -> Result<Player> {
        let entity_addr = memory.read_memory::<u64>(entity_list_addr + (index as u64 * 0x8))?;
        
        if entity_addr == 0 {
            return Err(anyhow!("实体地址无效"));
        }
        
        let health = memory.read_memory::<i32>(entity_addr + self.offsets.health)?;
        let team = memory.read_memory::<i32>(entity_addr + self.offsets.team)?;
        let dormant = memory.read_memory::<bool>(entity_addr + self.offsets.dormant)?;
        let spotted = memory.read_memory::<bool>(entity_addr + self.offsets.spotted)?;
        
        let pos_x = memory.read_memory::<f32>(entity_addr + self.offsets.pos_x)?;
        let pos_y = memory.read_memory::<f32>(entity_addr + self.offsets.pos_y)?;
        let pos_z = memory.read_memory::<f32>(entity_addr + self.offsets.pos_z)?;
        let view_offset_z = memory.read_memory::<f32>(entity_addr + self.offsets.view_offset_z)?;
        
        let position = Vector3::new(pos_x, pos_y, pos_z);
        let head_position = Vector3::new(pos_x, pos_y, pos_z + view_offset_z);
        
        // 计算与本地玩家的距离
        let distance = if let Some(local) = local_player {
            position.distance_to(&local.position)
        } else {
            0.0
        };
        
        let weapon_id = memory.read_memory::<i32>(entity_addr + self.offsets.active_weapon)
            .unwrap_or(0);
        
        Ok(Player {
            id: index,
            health,
            team,
            position,
            head_position,
            spotted,
            dormant,
            weapon_id,
            distance,
            screen_pos: None, // 屏幕坐标将在雷达模块中计算
        })
    }
}
