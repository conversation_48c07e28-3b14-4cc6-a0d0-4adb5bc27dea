use anyhow::Result;
use futures_util::{SinkExt, StreamExt};
use log::{info, debug, warn, error};
use serde_json;
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::{RwLock, broadcast};
use tokio_tungstenite::{accept_async, tungstenite::Message};
use uuid::Uuid;
use warp::Filter;

use crate::game::GameData;

pub struct WebServer {
    bind_addr: String,
    port: u16,
    game_data: Arc<RwLock<GameData>>,
}

impl WebServer {
    pub fn new(bind_addr: String, port: u16, game_data: Arc<RwLock<GameData>>) -> Self {
        Self {
            bind_addr,
            port,
            game_data,
        }
    }
    
    pub async fn run(&self) -> Result<()> {
        info!("启动Web服务器: {}:{}", self.bind_addr, self.port);
        
        let game_data = self.game_data.clone();
        
        // 创建广播通道用于实时数据推送
        let (tx, _) = broadcast::channel::<String>(1000);
        let tx = Arc::new(tx);
        
        // 启动数据广播任务
        let broadcast_task = {
            let tx = tx.clone();
            let game_data = game_data.clone();
            tokio::spawn(async move {
                Self::broadcast_game_data(tx, game_data).await;
            })
        };
        
        // 静态文件服务
        let static_files = warp::path("static")
            .and(warp::fs::dir("web/static"));
        
        // 主页
        let index = warp::path::end()
            .and(warp::fs::file("web/index.html"));
        
        // WebSocket端点
        let websocket = warp::path("ws")
            .and(warp::ws())
            .and(warp::any().map(move || tx.clone()))
            .map(|ws: warp::ws::Ws, tx: Arc<broadcast::Sender<String>>| {
                ws.on_upgrade(move |socket| Self::handle_websocket(socket, tx))
            });
        
        // API端点 - 获取当前游戏数据
        let api_data = warp::path!("api" / "data")
            .and(warp::get())
            .and(warp::any().map(move || game_data.clone()))
            .and_then(Self::get_game_data);
        
        // 组合所有路由
        let routes = index
            .or(static_files)
            .or(websocket)
            .or(api_data)
            .with(warp::cors().allow_any_origin());
        
        // 启动服务器
        let addr: std::net::SocketAddr = format!("{}:{}", self.bind_addr, self.port)
            .parse()
            .expect("无效的绑定地址");
        
        tokio::select! {
            _ = warp::serve(routes).run(addr) => {
                error!("Web服务器意外退出");
            }
            _ = broadcast_task => {
                error!("数据广播任务意外退出");
            }
        }
        
        Ok(())
    }
    
    async fn broadcast_game_data(
        tx: Arc<broadcast::Sender<String>>,
        game_data: Arc<RwLock<GameData>>,
    ) {
        let mut interval = tokio::time::interval(tokio::time::Duration::from_millis(50)); // 20 FPS
        
        loop {
            interval.tick().await;
            
            let data = {
                let game_data = game_data.read().await;
                game_data.clone()
            };
            
            match serde_json::to_string(&data) {
                Ok(json) => {
                    if let Err(e) = tx.send(json) {
                        debug!("广播数据失败: {}", e);
                    }
                }
                Err(e) => {
                    warn!("序列化游戏数据失败: {}", e);
                }
            }
        }
    }
    
    async fn handle_websocket(
        ws: warp::ws::WebSocket,
        tx: Arc<broadcast::Sender<String>>,
    ) {
        let client_id = Uuid::new_v4();
        info!("新的WebSocket连接: {}", client_id);
        
        let (mut ws_tx, mut ws_rx) = ws.split();
        let mut rx = tx.subscribe();
        
        // 处理传入消息的任务
        let incoming_task = tokio::spawn(async move {
            while let Some(result) = ws_rx.next().await {
                match result {
                    Ok(msg) => {
                        if msg.is_text() {
                            debug!("收到客户端消息: {}", msg.to_str().unwrap_or(""));
                        } else if msg.is_close() {
                            break;
                        }
                    }
                    Err(e) => {
                        warn!("WebSocket接收错误: {}", e);
                        break;
                    }
                }
            }
        });
        
        // 处理传出消息的任务
        let outgoing_task = tokio::spawn(async move {
            while let Ok(data) = rx.recv().await {
                if let Err(e) = ws_tx.send(Message::text(data)).await {
                    warn!("WebSocket发送错误: {}", e);
                    break;
                }
            }
        });
        
        // 等待任一任务完成
        tokio::select! {
            _ = incoming_task => {}
            _ = outgoing_task => {}
        }
        
        info!("WebSocket连接断开: {}", client_id);
    }
    
    async fn get_game_data(
        game_data: Arc<RwLock<GameData>>,
    ) -> Result<impl warp::Reply, warp::Rejection> {
        let data = game_data.read().await;
        Ok(warp::reply::json(&*data))
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::game::GameData;
    
    #[tokio::test]
    async fn test_web_server_creation() {
        let game_data = Arc::new(RwLock::new(GameData::new()));
        let server = WebServer::new("127.0.0.1".to_string(), 8080, game_data);
        
        assert_eq!(server.bind_addr, "127.0.0.1");
        assert_eq!(server.port, 8080);
    }
}
