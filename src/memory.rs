use anyhow::{Result, anyhow};
use log::{info, debug, warn};
use memflow::prelude::*;
use memflow_qemu::QemuConnector;
use memflow_win32::prelude::*;
use std::collections::HashMap;

pub struct MemoryReader {
    connector: QemuConnector,
    kernel: Win32Kernel<QemuConnector>,
    process: Win32Process<QemuConnector>,
    process_info: Win32ProcessInfo,
}

impl MemoryReader {
    pub async fn new(vm_name: &str, process_name: &str) -> Result<Self> {
        info!("连接到虚拟机: {}", vm_name);
        
        // 连接到QEMU虚拟机
        let connector = QemuConnector::new(vm_name)
            .map_err(|e| anyhow!("无法连接到虚拟机 {}: {}", vm_name, e))?;
        
        info!("初始化Windows内核...");
        let mut kernel = Win32Kernel::builder(connector.clone())
            .build_default()
            .map_err(|e| anyhow!("无法初始化Windows内核: {}", e))?;
        
        info!("查找目标进程: {}", process_name);
        let process_info = kernel
            .process_info_list()
            .map_err(|e| anyhow!("无法获取进程列表: {}", e))?
            .into_iter()
            .find(|p| p.name().to_lowercase().contains(&process_name.to_lowercase()))
            .ok_or_else(|| anyhow!("未找到进程: {}", process_name))?;
        
        info!("找到目标进程: {} (PID: {})", process_info.name(), process_info.pid());
        
        let process = kernel
            .into_process_by_info(process_info.clone())
            .map_err(|e| anyhow!("无法附加到进程: {}", e))?;
        
        Ok(Self {
            connector,
            kernel: Win32Kernel::builder(connector.clone()).build_default()?,
            process,
            process_info,
        })
    }
    
    /// 读取内存数据
    pub fn read_memory<T: Pod>(&mut self, address: Address) -> Result<T> {
        self.process
            .read(address)
            .map_err(|e| anyhow!("读取内存失败 0x{:x}: {}", address, e))
    }
    
    /// 读取内存缓冲区
    pub fn read_memory_buffer(&mut self, address: Address, size: usize) -> Result<Vec<u8>> {
        let mut buffer = vec![0u8; size];
        self.process
            .read_raw_into(address, &mut buffer)
            .map_err(|e| anyhow!("读取内存缓冲区失败 0x{:x}: {}", address, e))?;
        Ok(buffer)
    }
    
    /// 读取字符串
    pub fn read_string(&mut self, address: Address, max_len: usize) -> Result<String> {
        let buffer = self.read_memory_buffer(address, max_len)?;
        let null_pos = buffer.iter().position(|&b| b == 0).unwrap_or(buffer.len());
        String::from_utf8(buffer[..null_pos].to_vec())
            .map_err(|e| anyhow!("字符串解码失败: {}", e))
    }
    
    /// 获取模块基址
    pub fn get_module_base(&mut self, module_name: &str) -> Result<Address> {
        let modules = self.process
            .module_list()
            .map_err(|e| anyhow!("获取模块列表失败: {}", e))?;
        
        for module in modules {
            if module.name().to_lowercase().contains(&module_name.to_lowercase()) {
                debug!("找到模块 {}: 基址 0x{:x}", module.name(), module.base());
                return Ok(module.base());
            }
        }
        
        Err(anyhow!("未找到模块: {}", module_name))
    }
    
    /// 模式扫描
    pub fn pattern_scan(&mut self, pattern: &str, mask: &str, start: Address, size: usize) -> Result<Address> {
        let pattern_bytes: Vec<u8> = pattern
            .split_whitespace()
            .map(|s| u8::from_str_radix(s, 16))
            .collect::<Result<Vec<_>, _>>()
            .map_err(|e| anyhow!("无效的模式: {}", e))?;
        
        let mask_bytes: Vec<bool> = mask
            .chars()
            .map(|c| c == 'x')
            .collect();
        
        if pattern_bytes.len() != mask_bytes.len() {
            return Err(anyhow!("模式和掩码长度不匹配"));
        }
        
        let buffer = self.read_memory_buffer(start, size)?;
        
        for i in 0..=(buffer.len().saturating_sub(pattern_bytes.len())) {
            let mut found = true;
            for j in 0..pattern_bytes.len() {
                if mask_bytes[j] && buffer[i + j] != pattern_bytes[j] {
                    found = false;
                    break;
                }
            }
            if found {
                return Ok(start + i);
            }
        }
        
        Err(anyhow!("未找到模式"))
    }
    
    /// 检查进程是否仍然存在
    pub fn is_process_alive(&mut self) -> bool {
        self.kernel
            .process_info_list()
            .map(|list| {
                list.iter().any(|p| p.pid() == self.process_info.pid())
            })
            .unwrap_or(false)
    }
    
    /// 重新连接进程
    pub async fn reconnect(&mut self, process_name: &str) -> Result<()> {
        warn!("尝试重新连接进程: {}", process_name);
        
        let process_info = self.kernel
            .process_info_list()
            .map_err(|e| anyhow!("无法获取进程列表: {}", e))?
            .into_iter()
            .find(|p| p.name().to_lowercase().contains(&process_name.to_lowercase()))
            .ok_or_else(|| anyhow!("未找到进程: {}", process_name))?;
        
        self.process = self.kernel
            .into_process_by_info(process_info.clone())
            .map_err(|e| anyhow!("无法附加到进程: {}", e))?;
        
        self.process_info = process_info;
        info!("成功重新连接到进程");
        
        Ok(())
    }
}
