use anyhow::{Result, anyhow};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use crate::game::Vector3;

/// 地图配置结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MapConfig {
    /// 地图名称
    pub name: String,
    /// 地图显示名称
    pub display_name: String,
    /// 地图图片文件路径
    pub image_path: String,
    /// 地图边界坐标
    pub bounds: MapBounds,
    /// 地图缩放比例
    pub scale: f32,
    /// 地图旋转角度 (度)
    pub rotation: f32,
    /// 地图偏移
    pub offset: Vector3,
}

/// 地图边界
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MapBounds {
    /// 最小X坐标 (游戏世界坐标)
    pub min_x: f32,
    /// 最大X坐标
    pub max_x: f32,
    /// 最小Y坐标
    pub min_y: f32,
    /// 最大Y坐标
    pub max_y: f32,
    /// 最小Z坐标 (用于高度检测)
    pub min_z: f32,
    /// 最大Z坐标
    pub max_z: f32,
}

impl MapBounds {
    /// 检查坐标是否在地图范围内
    pub fn contains(&self, pos: &Vector3) -> bool {
        pos.x >= self.min_x && pos.x <= self.max_x &&
        pos.y >= self.min_y && pos.y <= self.max_y &&
        pos.z >= self.min_z && pos.z <= self.max_z
    }
    
    /// 获取地图中心点
    pub fn center(&self) -> Vector3 {
        Vector3::new(
            (self.min_x + self.max_x) / 2.0,
            (self.min_y + self.max_y) / 2.0,
            (self.min_z + self.max_z) / 2.0,
        )
    }
    
    /// 获取地图尺寸
    pub fn size(&self) -> Vector3 {
        Vector3::new(
            self.max_x - self.min_x,
            self.max_y - self.min_y,
            self.max_z - self.min_z,
        )
    }
}

/// 地图管理器
pub struct MapManager {
    /// 所有地图配置
    maps: HashMap<String, MapConfig>,
    /// 当前激活的地图
    current_map: Option<String>,
}

impl MapManager {
    pub fn new() -> Self {
        let mut manager = Self {
            maps: HashMap::new(),
            current_map: None,
        };
        
        // 初始化默认地图
        manager.init_default_maps();
        manager
    }
    
    /// 初始化默认地图配置
    fn init_default_maps(&mut self) {
        // Dust2 地图配置
        self.add_map(MapConfig {
            name: "de_dust2".to_string(),
            display_name: "Dust2".to_string(),
            image_path: "maps/de_dust2.png".to_string(),
            bounds: MapBounds {
                min_x: -2476.0,
                max_x: 1735.0,
                min_y: -2530.0,
                max_y: 2042.0,
                min_z: -103.0,
                max_z: 390.0,
            },
            scale: 4.4,
            rotation: 0.0,
            offset: Vector3::new(0.0, 0.0, 0.0),
        });
        
        // Mirage 地图配置
        self.add_map(MapConfig {
            name: "de_mirage".to_string(),
            display_name: "Mirage".to_string(),
            image_path: "maps/de_mirage.png".to_string(),
            bounds: MapBounds {
                min_x: -3230.0,
                max_x: 1713.0,
                min_y: -3401.0,
                max_y: 1682.0,
                min_z: -415.0,
                max_z: 300.0,
            },
            scale: 4.1,
            rotation: 0.0,
            offset: Vector3::new(0.0, 0.0, 0.0),
        });
        
        // Inferno 地图配置
        self.add_map(MapConfig {
            name: "de_inferno".to_string(),
            display_name: "Inferno".to_string(),
            image_path: "maps/de_inferno.png".to_string(),
            bounds: MapBounds {
                min_x: -2087.0,
                max_x: 3896.0,
                min_y: -3870.0,
                max_y: 1918.0,
                min_z: -144.0,
                max_z: 320.0,
            },
            scale: 4.9,
            rotation: 0.0,
            offset: Vector3::new(0.0, 0.0, 0.0),
        });
        
        // Cache 地图配置
        self.add_map(MapConfig {
            name: "de_cache".to_string(),
            display_name: "Cache".to_string(),
            image_path: "maps/de_cache.png".to_string(),
            bounds: MapBounds {
                min_x: -2000.0,
                max_x: 3250.0,
                min_y: -2814.0,
                max_y: 2663.0,
                min_z: 1613.0,
                max_z: 1740.0,
            },
            scale: 5.5,
            rotation: 0.0,
            offset: Vector3::new(0.0, 0.0, 0.0),
        });
        
        // Overpass 地图配置
        self.add_map(MapConfig {
            name: "de_overpass".to_string(),
            display_name: "Overpass".to_string(),
            image_path: "maps/de_overpass.png".to_string(),
            bounds: MapBounds {
                min_x: -4831.0,
                max_x: 1781.0,
                min_y: -3164.0,
                max_y: 1586.0,
                min_z: -611.0,
                max_z: 800.0,
            },
            scale: 5.2,
            rotation: 0.0,
            offset: Vector3::new(0.0, 0.0, 0.0),
        });
    }
    
    /// 添加地图配置
    pub fn add_map(&mut self, config: MapConfig) {
        self.maps.insert(config.name.clone(), config);
    }
    
    /// 获取地图配置
    pub fn get_map(&self, name: &str) -> Option<&MapConfig> {
        self.maps.get(name)
    }
    
    /// 获取当前地图
    pub fn current_map(&self) -> Option<&MapConfig> {
        self.current_map.as_ref().and_then(|name| self.maps.get(name))
    }
    
    /// 设置当前地图
    pub fn set_current_map(&mut self, name: &str) -> Result<()> {
        if self.maps.contains_key(name) {
            self.current_map = Some(name.to_string());
            Ok(())
        } else {
            Err(anyhow!("地图不存在: {}", name))
        }
    }
    
    /// 根据玩家位置自动检测地图
    pub fn detect_map(&mut self, player_pos: &Vector3) -> Option<&MapConfig> {
        for (name, config) in &self.maps {
            if config.bounds.contains(player_pos) {
                self.current_map = Some(name.clone());
                return Some(config);
            }
        }
        None
    }
    
    /// 获取所有地图列表
    pub fn get_all_maps(&self) -> Vec<&MapConfig> {
        self.maps.values().collect()
    }
    
    /// 世界坐标转地图坐标
    pub fn world_to_map(&self, world_pos: &Vector3, map_size: (u32, u32)) -> Option<(f32, f32)> {
        let config = self.current_map()?;
        
        // 计算相对位置 (0.0 到 1.0)
        let rel_x = (world_pos.x - config.bounds.min_x) / (config.bounds.max_x - config.bounds.min_x);
        let rel_y = (world_pos.y - config.bounds.min_y) / (config.bounds.max_y - config.bounds.min_y);
        
        // 应用旋转
        let (rot_x, rot_y) = if config.rotation != 0.0 {
            let angle = config.rotation.to_radians();
            let cos_a = angle.cos();
            let sin_a = angle.sin();
            
            let centered_x = rel_x - 0.5;
            let centered_y = rel_y - 0.5;
            
            let rotated_x = centered_x * cos_a - centered_y * sin_a + 0.5;
            let rotated_y = centered_x * sin_a + centered_y * cos_a + 0.5;
            
            (rotated_x, rotated_y)
        } else {
            (rel_x, rel_y)
        };
        
        // 转换为像素坐标
        let map_x = rot_x * map_size.0 as f32;
        let map_y = (1.0 - rot_y) * map_size.1 as f32; // Y轴翻转
        
        // 应用偏移
        let final_x = map_x + config.offset.x;
        let final_y = map_y + config.offset.y;
        
        Some((final_x, final_y))
    }
    
    /// 地图坐标转世界坐标
    pub fn map_to_world(&self, map_x: f32, map_y: f32, map_size: (u32, u32)) -> Option<Vector3> {
        let config = self.current_map()?;
        
        // 移除偏移
        let offset_x = map_x - config.offset.x;
        let offset_y = map_y - config.offset.y;
        
        // 转换为相对坐标
        let rel_x = offset_x / map_size.0 as f32;
        let rel_y = 1.0 - (offset_y / map_size.1 as f32); // Y轴翻转
        
        // 应用反向旋转
        let (unrot_x, unrot_y) = if config.rotation != 0.0 {
            let angle = -config.rotation.to_radians(); // 反向旋转
            let cos_a = angle.cos();
            let sin_a = angle.sin();
            
            let centered_x = rel_x - 0.5;
            let centered_y = rel_y - 0.5;
            
            let unrotated_x = centered_x * cos_a - centered_y * sin_a + 0.5;
            let unrotated_y = centered_x * sin_a + centered_y * cos_a + 0.5;
            
            (unrotated_x, unrotated_y)
        } else {
            (rel_x, rel_y)
        };
        
        // 转换为世界坐标
        let world_x = config.bounds.min_x + unrot_x * (config.bounds.max_x - config.bounds.min_x);
        let world_y = config.bounds.min_y + unrot_y * (config.bounds.max_y - config.bounds.min_y);
        let world_z = (config.bounds.min_z + config.bounds.max_z) / 2.0; // 使用中间高度
        
        Some(Vector3::new(world_x, world_y, world_z))
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_map_bounds() {
        let bounds = MapBounds {
            min_x: -1000.0,
            max_x: 1000.0,
            min_y: -1000.0,
            max_y: 1000.0,
            min_z: 0.0,
            max_z: 100.0,
        };
        
        let pos_inside = Vector3::new(0.0, 0.0, 50.0);
        let pos_outside = Vector3::new(2000.0, 0.0, 50.0);
        
        assert!(bounds.contains(&pos_inside));
        assert!(!bounds.contains(&pos_outside));
    }
    
    #[test]
    fn test_coordinate_conversion() {
        let mut manager = MapManager::new();
        manager.set_current_map("de_dust2").unwrap();
        
        let world_pos = Vector3::new(0.0, 0.0, 0.0);
        let map_coords = manager.world_to_map(&world_pos, (1024, 1024));
        
        assert!(map_coords.is_some());
        
        if let Some((x, y)) = map_coords {
            let back_to_world = manager.map_to_world(x, y, (1024, 1024));
            assert!(back_to_world.is_some());
        }
    }
}
