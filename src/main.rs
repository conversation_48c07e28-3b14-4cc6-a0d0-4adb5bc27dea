use anyhow::Result;
use clap::Parser;
use log::{info, error};
use std::sync::Arc;
use tokio::sync::RwLock;

mod memory;
mod game;
mod radar;
mod web;
mod maps;

use memory::MemoryReader;
use game::GameData;
use radar::RadarServer;
use maps::MapManager;

#[derive(Parser)]
#[command(name = "kvm-dma-radar")]
#[command(about = "KVM虚拟化DMA雷达系统")]
struct Args {
    /// QEMU虚拟机名称
    #[arg(short, long, default_value = "win10")]
    vm_name: String,
    
    /// Web服务器端口
    #[arg(short, long, default_value = "8080")]
    port: u16,
    
    /// 绑定地址
    #[arg(short, long, default_value = "0.0.0.0")]
    bind: String,
    
    /// 游戏进程名
    #[arg(short, long, default_value = "cs2.exe")]
    game_process: String,
    
    /// 调试模式
    #[arg(short, long)]
    debug: bool,
}

#[tokio::main]
async fn main() -> Result<()> {
    let args = Args::parse();
    
    // 初始化日志
    if args.debug {
        env_logger::Builder::from_env(env_logger::Env::default().default_filter_or("debug")).init();
    } else {
        env_logger::Builder::from_env(env_logger::Env::default().default_filter_or("info")).init();
    }
    
    info!("启动KVM-DMA雷达系统...");
    info!("目标虚拟机: {}", args.vm_name);
    info!("目标进程: {}", args.game_process);
    info!("Web服务器: {}:{}", args.bind, args.port);
    
    // 初始化内存读取器
    let memory_reader = Arc::new(RwLock::new(
        MemoryReader::new(&args.vm_name, &args.game_process).await?
    ));
    
    // 初始化游戏数据
    let game_data = Arc::new(RwLock::new(GameData::new()));

    // 初始化地图管理器
    let map_manager = Arc::new(RwLock::new(MapManager::new()));
    
    // 启动雷达服务器
    let radar_server = RadarServer::new(
        memory_reader.clone(),
        game_data.clone(),
        map_manager.clone(),
    );
    
    // 启动Web服务器
    let web_server = web::WebServer::new(
        args.bind.clone(),
        args.port,
        game_data.clone(),
        map_manager.clone(),
    );
    
    // 并发运行所有服务
    tokio::select! {
        result = radar_server.run() => {
            error!("雷达服务器退出: {:?}", result);
        }
        result = web_server.run() => {
            error!("Web服务器退出: {:?}", result);
        }
    }
    
    Ok(())
}
